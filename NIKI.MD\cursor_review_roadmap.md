cursor_review_roadmap.md (Entwurf)
🧱 Kategorie: Architekturbrüche
1. 🔥 Inkonsistente Dateipfade & Ladefehler (3.1)
Problem: items.json wird mit falschem Pfad geladen.

Risiko: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, schwer debuggbar.

Lösung: Einführung eines zentralen DataLoaders (Modul geplant).

Priorität: 🔥 Kritisch

Module: DataLoader.gd, Anpassung in InventorySystem, RecipeSystem, etc.

2. 🚧 Direktzugriffe auf Module (1.1 / 1.3)
Problem: GameMaster greift direkt auf unclear_resolver zu. LUCI auf Logbook (Autoload).

Risiko: Bruch des Kommunikationsprinzips. Spätere Umstrukturierung wird schwierig.

Lösung: Zugriff nur über autorisierte Getter/Dispatcher.

Priorität: 🧱 Hoch

Module: GameMaster, LUCI, ggf. neue Getter in LUCI.

3. ⚙️ Fehlende zentrale Datenkoordination (1.2)
Problem: Jeder lädt seine JSONs selbst.

Risiko: Redundanz, Inkonsistenz.

Lösung: DataLoader als zentrales Singleton-Autoload, mit load_data(path)-API.

Priorität: 🔁 Mittel → Hoch

Module: DataLoader.gd, Aufräumen in InventorySystem, RecipeSystem, StatusMaster.

🧠 Parser (LUCI) – Kernsystem
4. ✅ Verarbeitungsreihenfolge in LUCI (2.1)
Problem: AliasResolver kommt zu spät.

Risiko: CommandType-Matching schlägt fehl bei Synonymen.

Lösung: Reordering im LUCI.run(): Syntax → Alias → CommandType → Fuzzy

Priorität: ✅ Erledigt

Module: LUCI.gd, AliasResolver, CommandTypeMatcher

5. 🧰 UnclearResolver unvollständig (2.2 + 5.3)
Problem: Keine Top-3-Vorschläge, keine differenzierte Reaktion auf z. B. "use_placeholder".

Risiko: Spiel reagiert verwirrend auf Unsicherheiten.

Lösung: Ausbau UnclearResolver.gd:
→ Top-3-Suggestions bei "target_missing"
→ Differenzierte Rückgaben je nach origin

Priorität: 🔁 Mittel → Hoch

Module: UnclearResolver, LUCI, GameMaster

6. 🧪 Fehlende Craft-Detection bei „benutze X mit Y" (2.3)
Problem: Natürliches Crafting funktioniert nicht.

Risiko: Spieler erwartet Funktionalität, scheitert frustrierend.

Lösung: Semantische Craft-Erkennung in CommandTypeMatcher oder SyntaxTagger

Priorität: 🔥 Kritisch

Module: CommandTypeMatcher, CraftHandler, LUCI

📁 Datenhaltung & Struktur
7. 🧩 Rezepte hardcodiert (3.2 / 6.1)
Problem: Keine recipes.json

Risiko: Keine Trennung von Code und Daten.

Lösung: Strukturierte Rezepte in recipes.json, geladen via RecipeSystem.

Priorität: 🔁 Mittel

Module: RecipeSystem, DataLoader, neue Datei recipes.json

8. ⚠️ FuzzyMemory speichert ohne Bestätigung (5.2)
Problem: Gelerntes Verhalten ist evtl. falsch.

Risiko: Falschannahmen verfestigen sich.

Lösung: FuzzyMemory-Eintrag nur nach "ja"-Bestätigung.

Priorität: 🧱 Hoch

Module: FuzzyMemory.gd, YesNoResolver, GameMaster, Logbook (Bestätigungsprotokoll)

9. 📕 Fehlende ContextStack–Logbook-Integration (5.1)
Problem: Alte Kontexte werden nicht gespeichert.

Risiko: Kein Backtracking möglich, Kontext geht verloren.

Lösung: ContextStack on_overflow → Logbook

Priorität: 🔁 Mittel

Module: ContextStack, Logbook

🧱 Absicherung und Safeguards
10. ⛑️ Fehlende Fehlerbehandlung bei JSON-Load (4.1 / 4.3 / 6.3)
Problem: Keine try-catch-ähnlichen Abfänge.

Risiko: Crash bei Datenfehlern.

Lösung: JSON-Validation und sauberes Fehler-Logging in DataLoader.

Priorität: 🔥 Kritisch

Module: DataLoader, InventorySystem, RecipeSystem

11. 🧮 Inkonstante Rückgabewerte (4.2)
Problem: mal null, mal {} – nicht prüfbar.

Risiko: Logikfehler bei Weiterverarbeitung.

Lösung: Einheitsformat: z. B. {"success": false, "reason": "not found"} statt null

Priorität: 🔁 Mittel

Module: alle relevanten Rückgabemodule (SearchHandler, TakeHandler, etc.)

🧩 Parser-Vervollständigungen
12. 🔀 Rudimentärer CollisionResolver (5.3)
Problem: Mehrdeutigkeiten führen zu Fehlinterpretationen.

Risiko: Spiel führt falsche Aktionen aus.

Lösung: CollisionResolver mit Priorisierungslogik (Ort vs. Item vs. Ziel)

Priorität: 🔁 Mittel → Hoch

Module: CollisionResolver.gd, LUCI, ContextStack

13. 🔄 Zentrales Confidence-System (7.3)
Problem: Alle Parser-Module werden linear durchlaufen.

Risiko: Ineffizient, anfällig für Missverständnisse.

Lösung: ConfidenceEvaluator-Modul als Insight-System (nicht Logbook-relevant)
→ Saubere Schnittstellen zu LUCI, GameMaster, Suggestion-System
→ Keine automatischen Entscheidungen, nur Entscheidungsgrundlagen

Priorität: 🧱 Hoch (nach Abschluss des Parser-Umbaus und DebugUI-Vorbereitung)

Module: LUCI, neue ConfidenceEvaluator.gd, DebugUI

🧹 Cleanup & Struktur
14. 🧼 Temporäre Dateien im Projekt (7.1)
Problem: mai123.tmp etc. im Root.

Lösung: Sofort löschen, .gitignore prüfen.

Priorität: 🧹 Low, aber schnell erledigt

15. 🔧 Permanent aktiver Debug-Modus (7.2)
Problem: debug_mode = true im GameMaster

Risiko: Ungewollte Ausgaben, Performanceverlust

Lösung: Konfigurierbar via settings.json oder Startup-Flag

Priorität: Mittel

🔚 Fazit
Die Cursor-Analyse ist solide. Die gefährlichsten Punkte sind:

❗ Parser-Fehler (LUCI) → Reihenfolge, Craft-Erkennung, Unklarheiten

❗ Dateizugriff → falsche Pfade, fehlende Validierung, kein DataLoader

❗ Speicherprobleme → FuzzyMemory speichert zu früh, Logbook fehlt

Wir sollten nicht alles auf einmal machen, sondern in Prioritätsstufen aufteilen.

🎯 Vorschlag: Erstes Sprintziel (Sprint 01)
✅ LUCI-Reihenfolge fixen (erledigt) 
- DataLoader implementieren + Integration Inventory/Recipe
- Craft-Detection für "benutze X mit Y"
- FuzzyMemory auf Bestätigung umstellen
- UnclearResolver mit Top-3 starten

🎯 Empfohlene nächste Schritte (Sprint 02)
1. 🔥 Zweiphasigen Parser finalisieren
   - Clean Pass und Recovery Pass modularisieren (run_clean_pass(), run_recovery_pass())
   - Debug-Ausgaben pro Phase implementieren
   - ContextStack-Eintrag für erfolgreiche Phase hinzufügen

2. 🔥 DataLoader implementieren
   - Zentrales Modul für JSON-Datenzugriff
   - Fehlerbehandlung und Validierung
   - Integration in InventorySystem und RecipeSystem

3. 🧱 Craft-Detection implementieren
   - Semantische Erkennung von "benutze X mit Y" als CRAFT
   - Integration in CommandTypeMatcher oder SyntaxTagger

4. 🧱 FuzzyMemory-Bestätigung
   - Speicherung nur nach Spielerbestätigung
   - Integration mit YesNoResolver

5. 🔄 DebugUI-Grundlage schaffen
   - Einfache Anzeige für Parser-Entscheidungen
   - Vorbereitung für späteres Confidence-System

Diese Reihenfolge priorisiert die Stabilisierung des Parsers und die Behebung kritischer Datenzugriffsprobleme, bevor komplexere Features wie das zentrale Confidence-System implementiert werden.

