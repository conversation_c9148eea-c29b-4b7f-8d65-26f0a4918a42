# LUCI.md

## ✨ <PERSON><PERSON> von LUCI

LUCI ist unser modularer Parser für freie Spracheingaben. Ziel ist es, vage oder direkte Nutzereingaben zu einem strukturierten Spielkommando zu transformieren, das der GameMaster verarbeiten kann. Dabei sollen Unsicherheiten erkannt, bei Bedarf Rückfragen gestellt und bekannte Begriffe aus dem Kontext oder Logbuch genutzt werden. Langfristig kann LUCI durch ein LLM unterstützt werden, aktuell besteht LUCI aus rein modularen Subsystemen.

---

## 🔧 Aktueller Aufbau und Module

### Ablauf (Soll-Zustand)

LUCI soll folgende Reihenfolge zur Verarbeitung einhalten:

1. **SyntaxTagger**: Markiert relevante Satzteile, bestimmt Anfangsstruktur (z. B. Verb am Anfang, Zielwörter, Kombinationen).
2. **CommandTypeMatcher**: Weist die Eingabe einem CommandType zu (`TAKE`, `USE`, `LOOK`, `CRAFT`, etc.).
3. **AliasResolver**: Übersetzt Synonyme und Kurzformen ("nimm" = "nehme" = "take").
4. **FuzzyMemory**: Wenn keine exakte Zuordnung gelingt, werden verwandte Begriffe aus einer gespeicherten Ähnlichkeitsmatrix vorgeschlagen.
5. **UnclearResolver**: Wenn FuzzyMemory oder CommandTypeMatcher keine eindeutige Entscheidung liefern können, werden bis zu 3 Top-Vorschläge erzeugt (z. B. "Meintest du: A, B oder C?").
6. **ContextStack**: Nutzt vorherige Kontexte (z. B. zuletzt genannte Items oder Orte), wenn das aktuelle Target leer oder unklar ist.
7. **CollisionResolver**: Erkennt Mehrdeutigkeiten (z. B. Wort ist gleichzeitig Ort und Item) und leitet daraus eine Rückfrage ab.
8. **ParsedCommand**: Am Ende wird ein maschinenlesbares Kommando erzeugt.

> **Beispiel:** "benutze stein mit kokosnuss" → `USE` erkannt → Context gecheckt → Rezept gefunden → `CRAFT` wird erzeugt

---

## 🔎 Ist-Zustand

* `CommandTypeMatcher` funktioniert stabil bei klaren Befehlen.
* `SyntaxTagger` markiert erste Satzteile, ist aber bei komplexen Strukturen unzuverlässig.
* `FuzzyMemory` erkennt manche Schreibfehler, aber speichert Vorschläge zu früh.
* `UnclearResolver` ist noch nicht ausgebaut. Aktuell keine Mehrfachvorschläge bei Unsicherheit.
* `ContextStack` funktioniert, nutzt aber noch nicht das Logbuch als Backup.
* `ParsedCommand` wird erzeugt, aber kann inkonsistent sein (z. B. `USE` bei zwei kombinierbaren Items statt `CRAFT`).
* `CollisionResolver` ist nur rudimentär vorhanden.

---

## 🧠 Kontextsystem

Der `ContextStack` speichert die letzten max. 3 Einträge pro Typ (z. B. zuletzt genanntes Item, letzter Ort, letztes Rezept). Bei unklaren Targets (z. B. "nimm es") werden diese automatisch verwendet.

> **Erweiterung geplant**: Anbindung an das `Logbook`, um vergangene Tagesdaten oder explizite Nutzeraussagen zu analysieren.

---

## 📊 Vorschlagslogik & Confidence

* **Confidence-Score** wird für alle Matches berechnet (CommandType, Target, etc.).
* Bei niedriger Confidence (< 0.75) wird automatisch `UnclearResolver` aktiviert.
* Es werden bis zu **drei Top-Vorschläge** erstellt: z. B. bei "trink kokos" → Vorschläge: "trink kokoswasser", "iss kokosfleisch", "untersuche kokosnuss"
* Der Spieler muss bestätigen, welcher Vorschlag korrekt war. Erst dann wird der Vorschlag gespeichert (FuzzyMemory).

---

## ⚠️ Aktuelle Schwächen

* Kein Mechanismus zur Differenzierung von `USE` vs `CRAFT` bei Kombinationen.
* Suggestion-System (`UnclearResolver`) reagiert nicht konsistent.
* Keine gestaffelten Confidence-Werte.
* Vorschläge werden voreilig gespeichert.
* Kombination `ContextStack + Logbook` ist noch nicht aktiv.
* Keine modulare Priorisierung: Alle Module werden linear abgefragt, statt adaptiv.

---

## 🗓️ TODO-Liste (Parser-System)

| Status | Aufgabe                                                               |
| ------ | --------------------------------------------------------------------- |
| ❌      | Craft-Detection aus USE kombinieren ("benutze X mit Y" → `CRAFT`)     |
| ❌      | Suggestion-System mit Top-3-Vorschlägen in `UnclearResolver` umsetzen |
| ❌      | ContextStack an Logbook anbinden                                      |
| ❌      | Confidence-Stufen einführen und dynamisch verarbeiten                 |
| ❌      | FuzzyMemory-Speicherung nur nach Spieler-Bestätigung                  |
| ❌      | Kollisionsbehandlung für doppeldeutige Begriffe                       |
| ✅      | Basismodulstruktur angelegt und funktionsfähig                        |

---

## 🔹 Fazit

LUCI ist in der Basisstruktur funktionstüchtig, aber noch nicht stabil genug für komplexe Spracheingaben. Die Module greifen, aber viele kritische Situationen (Mehrdeutigkeiten, Vorschlagsrückfragen, CRAFT-Erkennung) fehlen oder sind zu unzuverlässig. Die stärkere Modularisierung mit Prioritäten und Confidence-Bewertung wird derzeit vorbereitet. Sobald LUCI robust läuft, kann die Anbindung eines LLMs zur Erweiterung erfolgen.
