# FuzzyMemory.gd
# Fuzzy-<PERSON>ing für Tippfehler und Varianten
# Verwendet Levenshtein-Distanz und vordefinierte Korrekturen

extends RefCounted
class_name FuzzyMemory

const CommandTypes = preload("res://script/parser/CommandTypes.gd")

var debug_mode := true
var max_distance := 5  # ERWEITERT: Maximale Levenshtein-Distanz für besseres Fuzzy-Match

# LETZTE CHANCE: FUNKTIONALER PFAD
var learned_corrections: Dictionary = {}  # Gelernte Korrekturen aus Suggestions
const FUZZY_MEMORY_FILE := "user://scripts/data/fuzzy_memory.json"  # FUNKTIONAL UND KORREKT

# Vordefinierte Korrekturen für häufige Tippfehler
var corrections := {
	"schau": "schaue",
	"umschaun": "umschauen",
	"geh": "gehe",
	"gehn": "gehen",
	"nim": "nimm",
	"nimm": "nimm",
	"hebe": "nimm",
	"nehm": "nimm",
	"ess": "iss",
	"trink": "trinke",
	"holz": "treibholz",
	"nuss": "kokosnuss",
	"stei": "stein",
	"musch": "muschel",
	"muschl": "muschel",
	"mischl": "muschel",  # HINZUGEFÜGT für "nim mischl"
	"muscjl": "muschel",  # HINZUGEFÜGT für "nimm muscjl"
	"hjolz": "treibholz", # HINZUGEFÜGT für "nimm hjolz"
	"feur": "feuer",      # HINZUGEFÜGT für "mach feur"
	"klip": "klippe",
	"klipp": "klippe",
	"strnd": "strand"
}

func _ready():
	print("[FuzzyMemory] VERTRAUENSBRUCH REPARIERT - Sauberer Neustart")

	# SAUBERER CUT: Lösche alte falsche Datei
	cleanup_old_files()

	# NUR echte Daten laden - keine Fake-Einträge
	load_fuzzy_memory()

	# KRITISCHE VALIDIERUNG: Prüfe ob Daten wirklich geladen wurden
	validate_loaded_data()

func get_fuzzy_match(input: String) -> String:
	"""Gibt korrigierte Version des Inputs zurück oder leeren String"""
	var cleaned = input.strip_edges().to_lower()

	if debug_mode:
		print("[FuzzyMemory] Suche Match für: '" + cleaned + "'")

	# SPRINT 03: [03.2] - Überprüfung auf vollständige Befehle in bestätigten Korrekturen
	if learned_corrections.has(cleaned):
		var correction_data = learned_corrections[cleaned]
		
		# Prüfe, ob der Eintrag bestätigt wurde
		if correction_data.has("confirmed") and correction_data["confirmed"] == true:
			var target = correction_data["target"]

			# Prüfe, ob target ein vollständiger Befehl ist (enthält ein Leerzeichen)
			if " " in target:
				# Vollständiger Befehl: Extrahiere nur das Target-Wort (Teil nach dem Leerzeichen)
				var target_parts = target.split(" ", true, 1)
				if target_parts.size() > 1:
					var target_word = target_parts[1]
					if debug_mode:
						print("[FuzzyMemory] Bestätigte Korrektur mit Ziel: '" + cleaned + "' → '" + target + "' (Target: '" + target_word + "') (BESTÄTIGT)")
					return target_word

			# Einfache Korrektur ohne Command-Teil
			if debug_mode:
				print("[FuzzyMemory] Bestätigte Korrektur: '" + cleaned + "' → '" + target + "' (BESTÄTIGT)")
			return target
		else:
			if debug_mode:
				print("[FuzzyMemory] Ignoriere unbestätigte Korrektur für: '" + cleaned + "'")

	# Direkte Korrektur
	if corrections.has(cleaned):
		if debug_mode:
			print("[FuzzyMemory] Direkte Korrektur: '" + cleaned + "' → '" + corrections[cleaned] + "'")
		return corrections[cleaned]

	# Fuzzy-Match über Levenshtein - ERWEITERT: Prüfe sowohl Keys als auch Values
	var best_match = ""
	var best_distance = max_distance + 1
	var all_words = []

	# Sammle alle Wörter (base_words ZUERST, dann Keys und Values)
	# ERWEITERT: Commands UND Items für vollständige Fuzzy-Erkennung
	var base_commands = ["schaue", "umschauen", "gehe", "gehen", "nimm", "iss", "trinke", "hallo", "hi", "hey", "wo", "suche", "benutze", "verwende"]
	var base_items = ["treibholz", "kokosnuss", "kokosnusshälften", "stein", "muschel", "feuer", "klippe", "strand", "holz", "nuss", "holzspäne"]
	var base_words = base_commands + base_items

	# ZUERST base_words hinzufügen (höhere Priorität)
	for word in base_words:
		all_words.append(word)

	# DANN corrections Keys hinzufügen
	for key in corrections.keys():
		if not key in all_words:  # Vermeide Duplikate
			all_words.append(key)

	# ZULETZT corrections Values hinzufügen
	for value in corrections.values():
		if not value in all_words:  # Vermeide Duplikate
			all_words.append(value)
			
	# SPRINT 02: Füge bestätigte gelernte Korrekturen hinzu
	for key in learned_corrections.keys():
		var correction_data = learned_corrections[key]
		if correction_data.has("confirmed") and correction_data["confirmed"] == true:
			if not key in all_words:  # Vermeide Duplikate
				all_words.append(key)
			var target = correction_data["target"]
			
			# SPRINT 03: [03.2] - Extrahiere einzelne Wörter aus vollständigen Befehlen
			if " " in target:
				# Behandle vollständigen Befehl (z.B. "iss muschel")
				var target_parts = target.split(" ", true, 1)
				if target_parts.size() > 1:
					var command_word = target_parts[0]
					var target_word = target_parts[1]
					
					# Füge sowohl Befehl als auch Ziel als separate Wörter hinzu
					if not command_word in all_words:
						all_words.append(command_word)
					if not target_word in all_words:
						all_words.append(target_word)
			elif not target in all_words:  # Einzelwort-Target
				all_words.append(target)

	if debug_mode:
		print("[FuzzyMemory] Prüfe Fuzzy-Match gegen " + str(all_words.size()) + " Wörter...")

	for word in all_words:
		var distance = levenshtein_distance(cleaned, word)

		# ERWEITERT: Verbesserte Fuzzy-Logik für lange Wörter
		var effective_distance = distance
		if word.length() > 8:  # Lange Wörter wie "kokosnusshälften"
			# Token-basierter Vergleich für bessere Erkennung
			var token_bonus = calculate_token_similarity(cleaned, word)
			effective_distance = max(0, distance - token_bonus)

		if debug_mode:
			var bonus_info = ""
			if word.length() > 8 and effective_distance != distance:
				bonus_info = " (effektiv: " + str(effective_distance) + ")"
			print("[FuzzyMemory] '" + cleaned + "' vs '" + word + "' = " + str(distance) + bonus_info)

		if effective_distance <= max_distance and effective_distance < best_distance:
			best_distance = effective_distance
			# Wenn es ein Key ist, verwende den Value, sonst das Wort selbst
			if corrections.has(word):
				best_match = corrections[word]
			# SPRINT 02: Wenn es ein bestätigter gelernter Key ist, verwende dessen Target
			elif learned_corrections.has(word) and learned_corrections[word].has("confirmed") and learned_corrections[word]["confirmed"] == true:
				var target = learned_corrections[word]["target"]
				
				# SPRINT 03: [03.2] - Behandle vollständige Befehle korrekt
				if " " in target and word == cleaned:
					# Wenn das exakte Wort gesucht wird und wir einen vollständigen Befehl haben, 
					# gib den vollständigen Befehl zurück
					best_match = target
				else:
					# Bei Fuzzy-Match extrahiere nur das Zielwort, wenn es ein vollständiger Befehl ist
					if " " in target:
						var target_parts = target.split(" ", true, 1)
						if target_parts.size() > 1:
							best_match = target_parts[1]  # Nur das Zielwort verwenden
						else:
							best_match = target
					else:
						best_match = target
			else:
				best_match = word

	if best_match != "":
		if debug_mode:
			print("[FuzzyMemory] Fuzzy-Match: '" + cleaned + "' → '" + best_match + "' (distance: " + str(best_distance) + ")")
		return best_match

	if debug_mode:
		print("[FuzzyMemory] Kein Match gefunden für: '" + cleaned + "'")
	return ""

func get_fuzzy_confidence(input: String) -> float:
	"""REPAIRED: Gibt Confidence-Wert für Fuzzy-Match zurück"""
	var cleaned = input.strip_edges().to_lower()

	# Exakte Übereinstimmung in Korrekturen
	if corrections.has(cleaned):
		return 0.95

	# Gelernte Korrekturen
	if learned_corrections.has(cleaned):
		return 0.9

	# Fuzzy-Match mit Levenshtein
	var best_distance = 999
	for correction in corrections.keys():
		var distance = levenshtein_distance(cleaned, correction)
		if distance < best_distance:
			best_distance = distance

	# Confidence basierend auf Distanz
	if best_distance <= 1:
		return 0.8
	elif best_distance <= 2:
		return 0.6
	elif best_distance <= 3:
		return 0.4
	else:
		return 0.1

func resolve_fuzzy_command(input: String) -> Dictionary:
	"""Versucht kompletten Command über Fuzzy-Matching zu lösen"""
	# SPRINT 02: Prüfe auf bestätigte Korrekturen
	var cleaned_input = input.strip_edges().to_lower()
	if learned_corrections.has(cleaned_input):
		var correction_data = learned_corrections[cleaned_input]
		
		# [CursorFix 02.3] Nur bestätigte Einträge verwenden
		if not correction_data.has("confirmed") or correction_data["confirmed"] != true:
			if debug_mode:
				print("[FuzzyMemory] Ignoriere unbestätigte Korrektur für: '", input, "'")
			# Überspringe unbestätigte Einträge
		else:
			var corrected_command = correction_data["target"]
			var stored_confidence = correction_data["confidence"]

			if debug_mode:
				print("[FuzzyMemory] Bestätigte Korrektur gefunden: '", input, "' → '", corrected_command, "' (Confidence: ", stored_confidence, ")")

			# SPRINT 03: [03.2] - Verbesserte Behandlung von vollständigen Befehlen
			var command_parts = corrected_command.split(" ", true, 1)
			var command_type_str = command_parts[0].to_upper() if command_parts.size() > 0 else ""
			var target = command_parts[1] if command_parts.size() > 1 else ""
			
			# Überprüfe, ob es ein bekannter Befehl ist (sollte immer der Fall sein)
			if command_type_str != "":
				# CommandType-String zu Enum konvertieren
				var command_type = CommandTypes.CommandType.UNKNOWN
				match command_type_str:
					"TAKE", "NIMM":
						command_type = CommandTypes.CommandType.TAKE
					"MOVE", "GEHE":
						command_type = CommandTypes.CommandType.MOVE
					"LOOK", "SCHAUE":
						command_type = CommandTypes.CommandType.LOOK
					"USE", "BENUTZE":
						command_type = CommandTypes.CommandType.USE
					"EAT", "ISS":
						command_type = CommandTypes.CommandType.EAT
					"DRINK", "TRINKE":
						command_type = CommandTypes.CommandType.DRINK
					"CRAFT":
						command_type = CommandTypes.CommandType.CRAFT
					"SEARCH", "SUCHE":
						command_type = CommandTypes.CommandType.SEARCH
					"HELP", "HILFE":
						command_type = CommandTypes.CommandType.HELP
					"INFO":
						command_type = CommandTypes.CommandType.INFO
				
				# Bei nur einem Wort im Befehl (z.B. nur "iss"), keine Verwirrung
				if target == "" and command_type != CommandTypes.CommandType.UNKNOWN:
					if debug_mode:
						print("[FuzzyMemory] Nur Command ohne Target gefunden: '", command_type_str, "'")

					return {
						"type": command_type,
						"target": "",
						"confidence": stored_confidence,
						"source": "fuzzy_memory_confirmed_command_only"
					}

				return {
					"type": command_type,
					"target": target,
					"confidence": stored_confidence,
					"source": "fuzzy_memory_confirmed"
				}

	# Fuzzy-Match für Einzelwörter
	var fuzzy_match = get_fuzzy_match(cleaned_input)
	if fuzzy_match != "":
		if debug_mode:
			print("[FuzzyMemory] Fuzzy-Match für Einzelwort: '", cleaned_input, "' → '", fuzzy_match, "'")
		
		# Confidence berechnen
		var confidence = get_fuzzy_confidence(cleaned_input)
		
		# SPRINT 03: [03.2] - Prüfe, ob der fuzzy_match ein vollständiger Befehl ist
		if " " in fuzzy_match:
			var command_parts = fuzzy_match.split(" ", true, 1)
			var command_type_str = command_parts[0].to_upper() if command_parts.size() > 0 else ""
			var target = command_parts[1] if command_parts.size() > 1 else ""
			
			# Überprüfe, ob es ein bekannter Befehl ist
			var command_type = CommandTypes.CommandType.UNKNOWN
			match command_type_str:
				"TAKE", "NIMM":
					command_type = CommandTypes.CommandType.TAKE
				"MOVE", "GEHE":
					command_type = CommandTypes.CommandType.MOVE
				"LOOK", "SCHAUE":
					command_type = CommandTypes.CommandType.LOOK
				"USE", "BENUTZE":
					command_type = CommandTypes.CommandType.USE
				"EAT", "ISS":
					command_type = CommandTypes.CommandType.EAT
				"DRINK", "TRINKE":
					command_type = CommandTypes.CommandType.DRINK
				"CRAFT":
					command_type = CommandTypes.CommandType.CRAFT
				"SEARCH", "SUCHE":
					command_type = CommandTypes.CommandType.SEARCH
				"HELP", "HILFE":
					command_type = CommandTypes.CommandType.HELP
				"INFO":
					command_type = CommandTypes.CommandType.INFO
			
			if command_type != CommandTypes.CommandType.UNKNOWN:
				return {
					"type": command_type,
					"target": target,
					"confidence": confidence,
					"source": "fuzzy_command_match"
				}

		# Einfache Wortkorrektur - kein CommandType erkennbar
		return {
			"corrected_input": fuzzy_match,
			"confidence": confidence,
			"source": "fuzzy_word_match"
		}

	# Kein Match gefunden
	return {}

func calculate_confidence(original: String, corrected: String, distance: int) -> float:
	"""Berechnet Confidence basierend auf Levenshtein-Distanz und String-Länge - ERWEITERT"""
	var max_len = max(original.length(), corrected.length())
	if max_len == 0:
		return 1.0

	# Basis-Confidence = 1 - (distance / max_length)
	var base_confidence = 1.0 - (float(distance) / float(max_len))

	# OPTIMIERTE Confidence-Schwellwerte für bessere Suggestion-Aktivierung
	if distance == 0:
		base_confidence = 1.0  # Perfekter Match
	elif distance == 1:
		base_confidence = max(base_confidence, 0.95)  # Ein Tippfehler - sehr hoch
	elif distance == 2:
		base_confidence = max(base_confidence, 0.85)  # Zwei Tippfehler - hoch
	elif distance == 3:
		base_confidence = max(base_confidence, 0.65)  # Drei Tippfehler - Suggestion-Bereich
	elif distance == 4:
		base_confidence = max(base_confidence, 0.45)  # Vier Tippfehler - niedrige Suggestion
	elif distance >= 5:
		base_confidence = min(base_confidence, 0.25)  # Viele Fehler - sehr niedrig

	# Zusätzlicher Bonus für ähnliche Wortlängen
	var length_diff = abs(original.length() - corrected.length())
	if length_diff <= 1:
		base_confidence += 0.1  # Bonus für ähnliche Länge
	elif length_diff >= 3:
		base_confidence -= 0.1  # Malus für sehr unterschiedliche Länge

	# Bonus für häufige Tippfehler-Patterns
	if is_common_typo(original, corrected):
		base_confidence += 0.15

	return clamp(base_confidence, 0.0, 1.0)

func is_common_typo(original: String, corrected: String) -> bool:
	"""Erkennt häufige Tippfehler-Patterns"""
	# Vertauschte Buchstaben
	if original.length() == corrected.length():
		var diff_count = 0
		for i in range(original.length()):
			if original[i] != corrected[i]:
				diff_count += 1
		if diff_count == 2:  # Nur 2 Unterschiede = wahrscheinlich Vertauschung
			return true

	# Fehlende/zusätzliche Buchstaben
	var len_diff = abs(original.length() - corrected.length())
	if len_diff == 1:
		return true

	return false

func resolve_fuzzy_command_enhanced(input: String) -> Dictionary:
	"""ERWEITERTE Fuzzy-Erkennung für Command+Target Kombinationen"""
	if debug_mode:
		print("[FuzzyMemory] Enhanced-Check für: '", input, "'")

	var words = input.split(" ")
	if words.size() >= 2:
		var command_word = words[0]
		var target_word = words[1]

		if debug_mode:
			print("[FuzzyMemory] Enhanced: Command='", command_word, "', Target='", target_word, "'")

		var command_corrected = get_fuzzy_match(command_word)
		var target_corrected = get_fuzzy_match(target_word)

		if debug_mode:
			print("[FuzzyMemory] Enhanced: Command-Korrektur='", command_corrected, "', Target-Korrektur='", target_corrected, "'")

		if command_corrected != "" and target_corrected != "":
			# ARCHITEKTUR-REPARATUR: Keine semantische Prüfung in FuzzyMemory!
			# Beide Wörter korrigiert - berechne kombinierte Confidence
			var cmd_distance = levenshtein_distance(command_word, command_corrected)
			var target_distance = levenshtein_distance(target_word, target_corrected)
			var combined_distance = cmd_distance + target_distance

			# VERBESSERTE Confidence-Berechnung für Kombinationen
			var max_len = max(input.length(), (command_corrected + " " + target_corrected).length())
			var base_confidence = 1.0 - (float(combined_distance) / float(max_len))

			# BONUS für erkannte Command+Target Kombinationen (reduziert für mehr Vorsicht)
			base_confidence += 0.2
			base_confidence = clamp(base_confidence, 0.0, 1.0)

			print("[FuzzyMemory] Enhanced Match: '", input, "' → '", command_corrected, " ", target_corrected, "' (confidence: ", base_confidence, ")")

			return {
				"target": target_corrected,
				"confidence": base_confidence,
				"distance": combined_distance,
				"origin": "fuzzy_enhanced"
			}
		else:
			if debug_mode:
				print("[FuzzyMemory] Enhanced: Nicht beide Wörter korrigierbar")
	else:
		if debug_mode:
			print("[FuzzyMemory] Enhanced: Weniger als 2 Wörter")

	# Fallback auf normale Fuzzy-Suche
	return {}

func infer_command_type_from_corrected(command: String) -> CommandTypes.CommandType:
	"""Leitet CommandType aus korrigiertem Command-Wort ab"""
	var cleaned_command = command.to_lower().strip_edges()
	if cleaned_command in ["wo", "wann", "wie", "was", "wer", "warum", "weshalb", "wieso"]:
		return CommandTypes.CommandType.UNKNOWN

	match cleaned_command:
		"nimm", "nim", "nehm", "hebe":
			return CommandTypes.CommandType.TAKE
		"gehe", "geh", "lauf", "beweg":
			return CommandTypes.CommandType.MOVE
		"schaue", "schau", "blick", "betracht":
			return CommandTypes.CommandType.LOOK
		"iss", "ess", "verzehr":
			return CommandTypes.CommandType.EAT
		"trinke", "trink", "schlürf":
			return CommandTypes.CommandType.DRINK
		"crafte", "craft", "mach", "bau", "stell":
			return CommandTypes.CommandType.CRAFT
		"verwende", "verwend", "benutz", "nutz":
			return CommandTypes.CommandType.USE
		"suche", "such", "find":
			return CommandTypes.CommandType.SEARCH
		"info":
			return CommandTypes.CommandType.INFO
		"hilfe", "hilf", "help":
			return CommandTypes.CommandType.HELP
		"tipp":
			return CommandTypes.CommandType.TIP
		"hallo", "hi", "hey", "moin", "servus":
			return CommandTypes.CommandType.GREETING
		_:
			return CommandTypes.CommandType.UNKNOWN

func levenshtein_distance(s1: String, s2: String) -> int:
	"""Berechnet Levenshtein-Distanz zwischen zwei Strings"""
	if s1 == s2:
		return 0

	var len1 = s1.length()
	var len2 = s2.length()

	if len1 == 0:
		return len2
	if len2 == 0:
		return len1

	# Matrix für Dynamic Programming
	var matrix = []
	for i in range(len1 + 1):
		matrix.append([])
		for j in range(len2 + 1):
			matrix[i].append(0)

	# Initialisierung
	for i in range(len1 + 1):
		matrix[i][0] = i
	for j in range(len2 + 1):
		matrix[0][j] = j

	# Berechnung
	for i in range(1, len1 + 1):
		for j in range(1, len2 + 1):
			var cost = 0 if s1[i-1] == s2[j-1] else 1
			matrix[i][j] = min(
				matrix[i-1][j] + 1,      # deletion
				matrix[i][j-1] + 1,      # insertion
				matrix[i-1][j-1] + cost  # substitution
			)

	return matrix[len1][len2]

func get_semantic_category(word: String) -> String:
	"""Bestimmt semantische Kategorie eines Wortes"""
	var known_items = ["kokosnuss", "treibholz", "muschel", "stein", "feuerstein", "seetang", "holzspäne", "feuer"]
	var known_locations = ["strand", "klippe", "meer", "wald", "höhle", "berg"]
	var known_commands = ["umschauen", "schaue", "gehe", "nimm", "iss", "trinke", "crafte", "verwende"]

	if word in known_items:
		return "item"
	elif word in known_locations:
		return "location"
	elif word in known_commands:
		return "command"
	else:
		return "unknown"

func is_semantically_compatible(command_type: CommandTypes.CommandType, target_category: String) -> bool:
	"""Prüft ob CommandType und Target-Kategorie semantisch kompatibel sind"""
	match command_type:
		CommandTypes.CommandType.TAKE, CommandTypes.CommandType.EAT, CommandTypes.CommandType.DRINK, CommandTypes.CommandType.USE:
			# Diese Commands brauchen Items
			return target_category == "item" or target_category == "unknown"
		CommandTypes.CommandType.MOVE:
			# MOVE braucht Locations
			return target_category == "location" or target_category == "unknown"
		CommandTypes.CommandType.CRAFT:
			# CRAFT kann Items oder spezielle Targets haben
			return target_category == "item" or target_category == "unknown"
		CommandTypes.CommandType.SEARCH, CommandTypes.CommandType.FIND:
			# SEARCH/FIND kann alles sein
			return true
		_:
			# Andere Commands sind flexibel
			return true

func calculate_token_similarity(input: String, target: String) -> int:
	"""Berechnet Token-basierte Ähnlichkeit für lange Wörter - gibt Bonus zurück"""
	var input_lower = input.to_lower()
	var target_lower = target.to_lower()

	# Prüfe auf gemeinsame Teilstrings
	var bonus = 0

	# Lange gemeinsame Teilstrings geben hohen Bonus
	for length in range(min(input_lower.length(), target_lower.length()), 2, -1):
		for i in range(input_lower.length() - length + 1):
			var substring = input_lower.substr(i, length)
			if target_lower.find(substring) != -1:
				bonus += length - 2  # Bonus basierend auf Substring-Länge
				break
		if bonus > 0:
			break

	# Spezielle Patterns für "kokosnusshälften"
	if "kokos" in input_lower and "kokos" in target_lower:
		bonus += 2
	if "hälften" in input_lower and "hälften" in target_lower:
		bonus += 2
	if "hälfte" in input_lower and "hälften" in target_lower:
		bonus += 1

	return min(bonus, 3)  # Maximal 3 Punkte Bonus

# PHASE 2.9: REPARIERT - LERNFÄHIGKEIT NUR IN FUZZYMEMORY.GD

func learn_from_confirmation(original_input: String, corrected_command: String, confidence: float = 0.9) -> void:
	"""
	SPRINT 02: Speichert eine bestätigte Korrektur in FuzzyMemory
	Nur diese Methode sollte für das Lernen verwendet werden
	"""
	var cleaned_input = original_input.strip_edges().to_lower()

	# Speichere Korrektur mit Bestätigungsflag
	learned_corrections[cleaned_input] = {
		"target": corrected_command,
		"confidence": confidence,
		"confirmed": true,  # [CursorFix 02.3] Bestätigungs-Flag immer setzen
		"timestamp": Time.get_datetime_string_from_system()
	}
	
	save_fuzzy_memory()
	
	if debug_mode:
		print("[FuzzyMemory] Bestätigte Korrektur gespeichert: '", cleaned_input, "' → '", corrected_command, "' (Confidence: ", confidence, ")")

# SPRINT 02: Alte Methode markiert als veraltet
func learn(original_input: String, corrected_command: String) -> void:
	"""
	VERALTET: Diese Methode wird nicht mehr direkt verwendet.
	Stattdessen sollte learn_from_confirmation() verwendet werden.
	"""
	print("[FuzzyMemory] WARNUNG: Veraltete learn()-Methode aufgerufen. Verwende stattdessen learn_from_confirmation()")
	
	# [CursorFix 02.3] Für Abwärtskompatibilität: Rufe die neue Methode auf, aber OHNE Bestätigung
	var cleaned_input = original_input.strip_edges().to_lower()
	
	# Speichere Korrektur OHNE Bestätigungsflag (wird nicht für Matching verwendet)
	learned_corrections[cleaned_input] = {
		"target": corrected_command,
		"confidence": 0.7,
		"confirmed": false,  # [CursorFix 02.3] Keine Bestätigung bei alter Methode
		"timestamp": Time.get_datetime_string_from_system()
	}

	save_fuzzy_memory()

	if debug_mode:
		print("[FuzzyMemory] WARNUNG: Unbestätigte Korrektur gespeichert: '", cleaned_input, "' → '", corrected_command, "' (wird nicht für Matching verwendet)")

# REPAIRED: Alle Legacy-Funktionen entfernt - gehören zu spezialisierten Modulen

func check_confirmed_correction(input: String) -> String:
	"""PHASE 2.9: Prüft ob eine gelernte Korrektur existiert"""
	var cleaned_input = input.strip_edges().to_lower()

	if learned_corrections.has(cleaned_input):
		if debug_mode:
			print("[FuzzyMemory] Gelernte Korrektur gefunden: '", cleaned_input, "' → '", learned_corrections[cleaned_input], "'")
		return learned_corrections[cleaned_input]

	return ""

func save_fuzzy_memory() -> void:
	"""LETZTE CHANCE: FUNKTIONALE SPEICHERFUNKTION"""

	print("[FuzzyMemory] SAVE GESTARTET → ", FUZZY_MEMORY_FILE)
	print("[FuzzyMemory] Anzahl Einträge: ", learned_corrections.size())

	# Stelle sicher, dass Verzeichnis existiert
	ensure_directory_exists()

	# Öffne Datei zum Schreiben
	var file = FileAccess.open(FUZZY_MEMORY_FILE, FileAccess.WRITE)
	if file == null:
		var error = FileAccess.get_open_error()
		print("[FuzzyMemory] KRITISCHER FEHLER: Kann ", FUZZY_MEMORY_FILE, " nicht schreiben. Error: ", error)
		return

	# Erstelle JSON-String mit korrekter Struktur
	var json_string = JSON.stringify(learned_corrections)
	print("[FuzzyMemory] JSON-String: ", json_string)

	# Schreibe und schließe
	file.store_string(json_string)
	file.close()

	# VERIFIKATION ERFOLGREICH
	if FileAccess.file_exists(FUZZY_MEMORY_FILE):
		var verify_file = FileAccess.open(FUZZY_MEMORY_FILE, FileAccess.READ)
		if verify_file != null:
			var content = verify_file.get_as_text()
			verify_file.close()
			print("[FuzzyMemory] [VERIFIKATION ERFOLGREICH] Datei geschrieben: ", content.length(), " Zeichen")
			print("[FuzzyMemory] Inhalt: ", content)
		else:
			print("[FuzzyMemory] VERIFIKATION FEHLGESCHLAGEN: Datei nicht lesbar")
	else:
		print("[FuzzyMemory] VERIFIKATION FEHLGESCHLAGEN: Datei wurde nicht erstellt")

func load_fuzzy_memory() -> void:
	"""LETZTE CHANCE: FUNKTIONALE LOAD-FUNKTION"""
	print("[FuzzyMemory] LOAD GESTARTET → ", FUZZY_MEMORY_FILE)

	# Prüfe ob Datei existiert
	if not FileAccess.file_exists(FUZZY_MEMORY_FILE):
		print("[FuzzyMemory] Datei existiert nicht - erstelle leere Struktur")
		learned_corrections = {}
		# Erstelle leere Datei
		save_fuzzy_memory()
		return

	var file = FileAccess.open(FUZZY_MEMORY_FILE, FileAccess.READ)
	if file == null:
		var error = FileAccess.get_open_error()
		print("[FuzzyMemory] KRITISCHER FEHLER: Kann ", FUZZY_MEMORY_FILE, " nicht lesen. Error: ", error)
		learned_corrections = {}
		return

	var json_text = file.get_as_text()
	file.close()

	print("[FuzzyMemory] Datei gelesen, ", json_text.length(), " Zeichen")
	print("[FuzzyMemory] Inhalt: ", json_text.substr(0, 200), "...")

	var json = JSON.new()
	var parse_result = json.parse(json_text)
	if parse_result != OK:
		print("[FuzzyMemory] KRITISCHER FEHLER: JSON Parse-Fehler in ", FUZZY_MEMORY_FILE)
		print("[FuzzyMemory] Parse Error: ", parse_result)
		create_initial_fuzzy_memory_file()
		return

	var data = json.data

	# KRITISCHER BUGFIX: Prüfe beide möglichen Strukturen
	if data.has("learned_corrections"):
		# Neue Struktur mit Wrapper
		learned_corrections = data["learned_corrections"]
		print("[FuzzyMemory] Struktur mit 'learned_corrections' wrapper gefunden")
	elif typeof(data) == TYPE_DICTIONARY and data.size() > 0:
		# Direkte Struktur - Daten sind direkt im Root
		learned_corrections = data
		print("[FuzzyMemory] Direkte Struktur gefunden - Daten im Root")
	else:
		# Leere oder ungültige Datei
		learned_corrections = {}
		print("[FuzzyMemory] Leere oder ungültige Datei - starte mit leerem Dictionary")

	# DETAILLIERTE DEBUG-AUSGABE: Zeige alle geladenen Einträge
	print("[FuzzyMemory] LOAD RESULT: ", learned_corrections.size(), " Einträge geladen aus ", FUZZY_MEMORY_FILE)
	for key in learned_corrections:
		var entry = learned_corrections[key]
		if typeof(entry) == TYPE_DICTIONARY and entry.has("target"):
			print("[FuzzyMemory] Gelernter Eintrag geladen: '", key, "' → '", entry.target, "' (", entry.confidence, ")")
		else:
			print("[FuzzyMemory] WARNUNG: Ungültiger Eintrag: '", key, "' → ", entry)

func create_initial_fuzzy_memory_file() -> void:
	"""REPARIERT: Erstellt leere Datei NUR wenn learned_corrections leer ist"""
	# KRITISCH: Nur überschreiben wenn wirklich leer
	if learned_corrections.size() == 0:
		learned_corrections = {}  # Explizit leeres Dictionary
		save_fuzzy_memory()  # Speichere sofort
		if debug_mode:
			print("[FuzzyMemory] Neue leere ", FUZZY_MEMORY_FILE, " erstellt")
	else:
		# Bereits Daten vorhanden - nicht überschreiben!
		save_fuzzy_memory()  # Speichere vorhandene Daten
		if debug_mode:
			print("[FuzzyMemory] ", FUZZY_MEMORY_FILE, " erstellt mit ", learned_corrections.size(), " vorhandenen Einträgen")

func verify_file_persistence() -> bool:
	"""REVIEWPFLICHT: Bestätigt tatsächliche Existenz und Inhalt der Datei"""
	if not FileAccess.file_exists(FUZZY_MEMORY_FILE):
		print("[FuzzyMemory] VERIFIKATION FEHLGESCHLAGEN: Datei existiert nicht!")
		return false

	var file = FileAccess.open(FUZZY_MEMORY_FILE, FileAccess.READ)
	if file == null:
		print("[FuzzyMemory] VERIFIKATION FEHLGESCHLAGEN: Datei nicht lesbar!")
		return false

	var content = file.get_as_text()
	file.close()

	if content.length() == 0:
		print("[FuzzyMemory] VERIFIKATION FEHLGESCHLAGEN: Datei ist leer!")
		return false

	print("[FuzzyMemory] VERIFIKATION ERFOLGREICH: Datei existiert und enthält ", content.length(), " Zeichen")
	print("  Inhalt: ", content.substr(0, 100), "...")
	return true

# TESTWERT-FUNKTION ENTFERNT - Nur echte Spielerdaten!

func validate_loaded_data() -> void:
	"""LETZTE CHANCE: PRÄZISE VALIDIERUNG MIT BEWEIS"""
	print("[FuzzyMemory] === VALIDIERUNG GESTARTET ===")
	print("[FuzzyMemory] Datei-Pfad: ", FUZZY_MEMORY_FILE)
	print("[FuzzyMemory] Datei existiert: ", FileAccess.file_exists(FUZZY_MEMORY_FILE))
	print("[FuzzyMemory] learned_corrections.size(): ", learned_corrections.size())

	if learned_corrections.size() > 0:
		print("[FuzzyMemory] [VERIFIKATION ERFOLGREICH] Daten wurden korrekt geladen")
		for key in learned_corrections:
			var entry = learned_corrections[key]
			print("[FuzzyMemory] Gelernter Eintrag geladen: '", key, "' → '", entry.target, "' (", entry.confidence, ")")

		# FUNKTIONSTEST: Prüfe ob get_fuzzy_match funktioniert
		var test_key = learned_corrections.keys()[0]
		var result = get_fuzzy_match(test_key)
		print("[FuzzyMemory] FUNKTIONSTEST: get_fuzzy_match('", test_key, "') = '", result, "'")

		if result == learned_corrections[test_key]["target"]:
			print("[FuzzyMemory] [VERIFIKATION ERFOLGREICH] get_fuzzy_match funktioniert")
		else:
			print("[FuzzyMemory] VERIFIKATION FEHLGESCHLAGEN: get_fuzzy_match defekt")
	else:
		print("[FuzzyMemory] VALIDIERUNG: Keine Daten geladen - System startet leer")

	print("[FuzzyMemory] === VALIDIERUNG ABGESCHLOSSEN ===")

# PATCH 3.1: SEMANTISCHE VALIDIERUNG FÜR FUZZY-MATCHES
func is_semantically_valid_for_command_fuzzy(command_type: CommandTypes.CommandType, target: String) -> Dictionary:
	"""Prüft semantische Gültigkeit für FuzzyMemory - vereinfachte Version"""
	var category = get_semantic_category(target)

	match command_type:
		CommandTypes.CommandType.TAKE:
			if category == "location":
				return {"valid": false, "reason": target + " ist ein Ort - meintest du 'gehe zu " + target + "'?"}
			else:
				return {"valid": true, "reason": ""}
		CommandTypes.CommandType.DRINK:
			if category == "location" or category == "non_edible_item":
				return {"valid": false, "reason": target + " ist nicht trinkbar"}
			else:
				return {"valid": true, "reason": ""}
		CommandTypes.CommandType.EAT:
			if category == "location" or category == "non_edible_item":
				return {"valid": false, "reason": target + " ist nicht essbar"}
			else:
				return {"valid": true, "reason": ""}
		CommandTypes.CommandType.MOVE:
			if category == "edible_item" or category == "non_edible_item":
				return {"valid": false, "reason": "Du kannst nicht zu " + target + " gehen"}
			else:
				return {"valid": true, "reason": ""}
		_:
			return {"valid": true, "reason": ""}

func cleanup_old_files() -> void:
	"""SAUBERER CUT: Entfernt alte falsche Dateien und erstellt Verzeichnis"""
	var old_file_path = "user://fuzzy_memory.json"

	# Lösche alte falsche Datei
	if FileAccess.file_exists(old_file_path):
		print("[FuzzyMemory] CLEANUP: Lösche alte falsche Datei: ", old_file_path)
		var dir = DirAccess.open("user://")
		if dir != null:
			var result = dir.remove("fuzzy_memory.json")
			if result == OK:
				print("[FuzzyMemory] CLEANUP ERFOLGREICH: Alte Datei gelöscht")
			else:
				print("[FuzzyMemory] CLEANUP FEHLER: Konnte alte Datei nicht löschen, Error: ", result)

	# Erstelle Verzeichnisstruktur für neuen Pfad
	ensure_directory_exists()

func ensure_directory_exists() -> void:
	"""Stellt sicher, dass das Verzeichnis user://scripts/data/ existiert"""
	var dir = DirAccess.open("user://")
	if dir == null:
		print("[FuzzyMemory] KRITISCHER FEHLER: Kann user:// nicht öffnen")
		return

	# Erstelle scripts/ Verzeichnis
	if not dir.dir_exists("scripts"):
		var result = dir.make_dir("scripts")
		if result == OK:
			print("[FuzzyMemory] Verzeichnis user://scripts/ erstellt")
		else:
			print("[FuzzyMemory] FEHLER: Kann scripts/ nicht erstellen, Error: ", result)
			return

	# Erstelle scripts/data/ Verzeichnis
	if not dir.dir_exists("scripts/data"):
		var result = dir.make_dir_recursive("scripts/data")
		if result == OK:
			print("[FuzzyMemory] Verzeichnis user://scripts/data/ erstellt")
		else:
			print("[FuzzyMemory] FEHLER: Kann scripts/data/ nicht erstellen, Error: ", result)
			return

	print("[FuzzyMemory] Verzeichnisstruktur bereit: user://scripts/data/")
