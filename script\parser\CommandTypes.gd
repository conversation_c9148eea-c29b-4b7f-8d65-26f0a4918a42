# CommandTypes.gd
# Globale Definition aller Befehlstypen für LUCI Parser-System
# Wird von allen Parser-Modulen verwendet

extends RefCounted

# Ursprüngliche class_name wiederhergestellt
class_name CommandTypes

# Befehlstypen enum - ZENTRAL DEFINIERT
enum CommandType {
	UNKNOWN,
	LOOK,
	MOVE,
	TAKE,
	EAT,
	DRINK,
	USE,
	CRAFT,
	INFO,
	HELP,
	TIP,
	INVENTORY,
	RECIPES,
	FIRE,
	TASKS,
	SEARCH,
	GREETING,
	LOCATION,
	FIND,
	CLEAR_CONTEXT,  # PHASE 2.8: Für "lösche kontext", "clear context" etc.
	QUERY_LOG  # 💬 LOGBOOK-INTEGRATION: Für "was gestern?", "was heute?" etc.
}

# ParsedCommand ist jetzt in separater Datei: ParsedCommand.gd
