# CommandTypes.gd
# Globale Definition aller Befehlstypen für LUCI Parser-System
# Wird von allen Parser-Modulen verwendet

extends RefCounted

# REPARIERT: class_name entfernt um Namenskonflikt zu vermeiden
# class_name CommandTypes

# Befehlstypen enum - ZENTRAL DEFINIERT
enum CommandType {
	UNKNOWN,
	LOOK,
	MOVE,
	TAKE,
	EAT,
	DRINK,
	USE,
	CRAFT,
	INFO,
	HELP,
	TIP,
	INVENTORY,
	RECIPES,
	FIRE,
	TASKS,
	SEARCH,
	GREETING,
	LOCATION,
	FIND,
	CLEAR_CONTEXT,  # PHASE 2.8: Für "lösche kontext", "clear context" etc.
	QUERY_LOG  # 💬 LOGBOOK-INTEGRATION: Für "was gestern?", "was heute?" etc.
}

# ParsedCommand ist jetzt in separater Datei: ParsedCommand.gd
