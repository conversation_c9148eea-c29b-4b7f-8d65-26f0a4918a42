extends Control

class_name DebugUI

# PHASE 2.7: ECHTE DEBUG-UI FÜR LUCI-ENTSCHEIDUNGEN
# Zeigt prägnante LUCI-Informationen für Entwicklung und Testing

@onready var debug_label = $PanelDebug/DebugScroll/DebugLabel
@onready var copy_button = $PanelDebug/CopyButton
@onready var debug_panel = $PanelDebug

var debug_messages: Array[String] = []
var max_debug_messages: int = 50
var show_luci_debug: bool = true

# PHASE 2.7: LUCI-SPEZIFISCHE DEBUG-DATEN
var last_suggestion: Dictionary = {}
var last_learning: Dictionary = {}
var last_command: Dictionary = {}

# SPRINT 02: Confidence-Report-Daten
var confidence_reports: Array[Dictionary] = []
var max_confidence_reports: int = 10

# SPRINT 03: [03.5] Debug-Modi und Status-Flags
var debug_mode: bool = true
var has_ui_elements: bool = false
var show_in_console: bool = true
var show_in_ui: bool = false  # Standardmäßig deaktiviert, kann per Methode aktiviert werden

# SPRINT 03: [03.5] S<PERSON>ichert die letzten Confidence Reports nach Typ
var last_reports: Dictionary = {
	"clean_pass": {},
	"recovery_pass": {},
	"final_result": {},
	"recovery_missing_target": {},  # SPRINT 03: [03.5] - Neuer Typ für fehlende Targets
	"recovery_fuzzy_direct": {},
	"fuzzy_suggestion": {},
	"context_suggestion": {}
}

func _ready():
	# SPRINT 03: [03.5] Prüfe UI-Elemente
	if debug_panel == null or debug_label == null:
		push_warning("[DebugUI] WARNUNG: UI-Elemente nicht gefunden!")
		has_ui_elements = false
	else:
		has_ui_elements = true
		
	# Standardmäßig Debug-UI ausblenden
	if has_ui_elements and not show_in_ui:
		debug_panel.visible = false
	
	print("[DebugUI] Initialisiert - UI aktiv: ", has_ui_elements, ", UI sichtbar: ", show_in_ui)
	
	# Debug-Label konfigurieren
	if debug_label:
		debug_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
		debug_label.vertical_alignment = VERTICAL_ALIGNMENT_TOP
		debug_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
		debug_label.clip_contents = true

	# Kopier-Button verbinden
	if copy_button:
		copy_button.pressed.connect(_on_copy_button_pressed)

# === LUCI-DEBUG-AUSGABEN ===

func add_luci_debug(message: String) -> void:
	"""PHASE 2.7: Fügt LUCI-Debug-Nachricht hinzu"""
	if not show_luci_debug:
		return
	
	# Zeitstempel hinzufügen
	var time_dict = Time.get_datetime_dict_from_system()
	var time_str = String("%02d:%02d:%02d" % [time_dict.hour, time_dict.minute, time_dict.second])
	var timestamped_message = "[" + time_str + "] " + message
	
	debug_messages.append(timestamped_message)
	
	# Auch in Godot-Konsole ausgeben
	print("[DebugUI] " + message)
	
	# Nur die letzten N Nachrichten behalten
	if debug_messages.size() > max_debug_messages:
		debug_messages = debug_messages.slice(-max_debug_messages)
	
	update_debug_display()

func update_luci_decision(command_type: String, target: String, confidence: float, source: String) -> void:
	"""PHASE 2.7: Aktualisiert letzte LUCI-Entscheidung"""
	last_command = {
		"command_type": command_type,
		"target": target,
		"confidence": confidence,
		"source": source,
		"timestamp": Time.get_datetime_string_from_system()
	}
	
	var debug_message = "[LUCI] %s → %s (%.1f via %s)" % [command_type, target, confidence, source]
	add_luci_debug(debug_message)

func update_suggestion(suggestion_text: String, original_input: String = "") -> void:
	"""PHASE 2.7: Aktualisiert letzte Suggestion"""
	last_suggestion = {
		"text": suggestion_text,
		"original_input": original_input,
		"timestamp": Time.get_datetime_string_from_system()
	}
	
	var debug_message = "[SUGGESTION] %s" % suggestion_text
	if original_input != "":
		debug_message += " (für: '%s')" % original_input
	add_luci_debug(debug_message)

func update_learning(original_input: String, corrected_command: String) -> void:
	"""PHASE 2.7: Aktualisiert letzte Lernkorrektur"""
	last_learning = {
		"original_input": original_input,
		"corrected_command": corrected_command,
		"timestamp": Time.get_datetime_string_from_system()
	}
	
	var debug_message = "[LEARNING] '%s' → '%s'" % [original_input, corrected_command]
	add_luci_debug(debug_message)

func update_pronoun_resolution(pronoun: String, resolved_item: String, method: String) -> void:
	"""PHASE 2.7: Aktualisiert Pronomen-Auflösung"""
	var debug_message = "[PRONOMEN] '%s' → %s (via %s)" % [pronoun, resolved_item, method]
	add_luci_debug(debug_message)

# === SPRINT 02: CONFIDENCE-REPORT-FUNKTIONEN ===

func show_confidence_report(report: Dictionary) -> void:
	if not debug_mode:
		return
		
	if report.is_empty():
		print("[DebugUI] Leerer Report ignoriert")
		return
	
	# Report speichern nach Typ
	var phase = report.get("phase", "unknown")
	if phase != "unknown":
		last_reports[phase] = report.duplicate()
	
	# Recovery Reason speichern, wenn vorhanden
	var details = report.get("details", {})
	var recovery_reason = details.get("recovery_reason", "")
	if recovery_reason == "missing_target":
		# SPRINT 03: [03.5] Speichere speziell für missing_target
		last_reports["recovery_missing_target"] = report.duplicate()
	
	# Debug-Ausgabe in Konsole
	if show_in_console:
		print_debug_report(report)
	
	# UI-Ausgabe wenn aktiv
	if has_ui_elements and show_in_ui:
		update_debug_ui(report)

func get_confidence_reports() -> Array:
	"""Gibt alle gespeicherten Confidence-Reports zurück"""
	return confidence_reports

func clear_confidence_reports() -> void:
	"""Löscht alle gespeicherten Confidence-Reports"""
	confidence_reports.clear()
	add_luci_debug("[CONFIDENCE] Reports gelöscht")
	update_debug_display()

# === ALLGEMEINE DEBUG-FUNKTIONEN ===

func add_debug(message: String) -> void:
	"""Allgemeine Debug-Nachricht hinzufügen"""
	add_luci_debug(message)

func update_debug_display() -> void:
	"""Aktualisiert die Debug-Anzeige"""
	if debug_label == null:
		return
	
	# Debug-Text zusammenbauen
	var debug_text = ""
	
	# PHASE 2.7: LUCI-STATUS-ÜBERSICHT
	debug_text += "=== LUCI STATUS ===\n"
	
	if not last_command.is_empty():
		debug_text += "Letzter Befehl: %s → %s (%.1f)\n" % [
			last_command.get("command_type", "?"),
			last_command.get("target", "?"),
			last_command.get("confidence", 0.0)
		]
	
	if not last_suggestion.is_empty():
		debug_text += "Letzte Suggestion: %s\n" % last_suggestion.get("text", "?")
	
	if not last_learning.is_empty():
		debug_text += "Letzte Korrektur: '%s' → '%s'\n" % [
			last_learning.get("original_input", "?"),
			last_learning.get("corrected_command", "?")
		]
	
	# SPRINT 02: Confidence-Reports anzeigen
	if not confidence_reports.is_empty():
		debug_text += "\n=== CONFIDENCE REPORTS ===\n"
		for i in range(min(3, confidence_reports.size())):  # Zeige nur die letzten 3 Reports
			var report = confidence_reports[confidence_reports.size() - 1 - i]
			debug_text += "%s: %s → %s (%.2f)\n" % [
				report.get("phase", "unknown"),
				report.get("input", ""),
				report.get("command_type", "UNKNOWN"),
				report.get("confidence", 0.0)
		]
	
	debug_text += "\n=== DEBUG LOG ===\n"
	
	# Debug-Nachrichten hinzufügen
	for message in debug_messages:
		debug_text += message + "\n"
	
	debug_label.text = debug_text
	
	# Scroll zum Ende
	call_deferred("_scroll_debug_to_bottom")

func _scroll_debug_to_bottom() -> void:
	"""Scrollt Debug-Anzeige zum Ende"""
	var debug_scroll = $PanelDebug/DebugScroll
	if debug_scroll and debug_scroll is ScrollContainer:
		var v_scroll = debug_scroll.get_v_scroll_bar()
		if v_scroll:
			debug_scroll.scroll_vertical = int(v_scroll.max_value)

func _on_copy_button_pressed() -> void:
	"""Kopiert Debug-Text in Zwischenablage"""
	print("[DebugUI] Copy Button gedrückt!")
	if debug_label == null:
		print("[ERROR] Debug Label ist null!")
		return
	if debug_label.text.length() == 0:
		print("[ERROR] Debug Label ist leer!")
		return

	print("[DebugUI] Kopiere Text: " + str(debug_label.text.length()) + " Zeichen")
	DisplayServer.clipboard_set(debug_label.text)
	print("[INFO] Debug-Text in Zwischenablage kopiert!")

	# Visuelles Feedback
	copy_button.text = "✓"
	await get_tree().create_timer(1.0).timeout
	copy_button.text = "📋"

# === UTILITY-FUNKTIONEN ===

func clear_debug() -> void:
	"""Löscht alle Debug-Nachrichten"""
	debug_messages.clear()
	last_suggestion.clear()
	last_learning.clear()
	last_command.clear()
	confidence_reports.clear()  # SPRINT 02: Auch Confidence-Reports löschen
	update_debug_display()
	print("[DebugUI] Debug-Log geleert")

func toggle_luci_debug() -> void:
	"""Schaltet LUCI-Debug-Ausgaben ein/aus"""
	show_luci_debug = !show_luci_debug
	print("[DebugUI] LUCI-Debug: ", "EIN" if show_luci_debug else "AUS")

func get_debug_summary() -> String:
	var summary = "=== LUCI DEBUG SUMMARY ===\n"
	
	if last_reports.get("final_result", {}).is_empty():
		return summary + "Keine Debug-Daten verfügbar."
	
	var final = last_reports.get("final_result", {})
	summary += "Ergebnis: %s '%s' (Conf: %.2f)\n" % [
		final.get("command_type", "?"),
		final.get("target", ""),
		final.get("confidence", 0.0)
	]
	
	# SPRINT 03: [03.5] Recovery-Grund prominenter anzeigen
	var details = final.get("details", {})
	var recovery_needed = details.get("recovery_needed", false)
	if recovery_needed:
		var reason = details.get("recovery_reason", "unknown")
		summary += "Recovery aktiviert: %s\n" % reason
		
		# Details zum Recovery-Pass
		if reason == "missing_target" and not last_reports.get("recovery_missing_target", {}).is_empty():
			var target_report = last_reports.get("recovery_missing_target", {})
			summary += "Missing Target Recovery: %s → '%s'\n" % [
				target_report.get("command_type", "?"),
				target_report.get("target", "")
			]
	
	# Clean Pass vs. Recovery Pass
	var clean = last_reports.get("clean_pass", {})
	if not clean.is_empty():
		summary += "Clean Pass: %s '%s'\n" % [
			clean.get("command_type", "?"),
			clean.get("target", "")
		]
	
	return summary

# SPRINT 03: [03.5] - Füge neue Hilfsmethode hinzu, um UI-Status zu ändern
func set_ui_visibility(visible: bool) -> void:
	show_in_ui = visible
	if has_ui_elements and debug_panel != null:
		debug_panel.visible = visible
	print("[DebugUI] UI-Sichtbarkeit geändert: ", visible)

# Formatiert und zeigt den Report in der Konsole an
func print_debug_report(report: Dictionary) -> void:
	var phase = report.get("phase", "unknown")
	var cmd_type = report.get("command_type", "UNKNOWN")
	var target = report.get("target", "")
	var confidence = report.get("confidence", 0.0)
	var source = report.get("source", "unknown")
	var details = report.get("details", {})
	
	# Basis-Ausgabe
	var debug_text = "[LUCI-DEBUG] === %s ===\n" % phase.to_upper()
	debug_text += "Command: %s, Target: '%s'\n" % [cmd_type, target]
	debug_text += "Confidence: %.2f, Source: %s\n" % [confidence, source]
	
	# Details ausgeben, wenn vorhanden
	if not details.is_empty():
		debug_text += "Details:\n"
		# SPRINT 03: [03.5] - Verbesserte Detail-Ausgabe
		for key in details:
			var value = details[key]
			if value is float:
				debug_text += "  %s: %.2f\n" % [key, value]
			else:
				debug_text += "  %s: %s\n" % [key, str(value)]
	
	# Recovery Reason prominenter anzeigen
	var recovery_reason = details.get("recovery_reason", "")
	if recovery_reason != "":
		debug_text += "RECOVERY REASON: %s\n" % recovery_reason
	
	print(debug_text)

# Aktualisiert die UI mit dem Report
func update_debug_ui(report: Dictionary) -> void:
	if not has_ui_elements or debug_label == null:
		return
		
	var phase = report.get("phase", "unknown")
	var cmd_type = report.get("command_type", "UNKNOWN")
	var target = report.get("target", "")
	var confidence = report.get("confidence", 0.0)
	var source = report.get("source", "unknown")
	
	# Einfaches UI-Format
	var ui_text = "LUCI: %s (%s)\n" % [cmd_type, phase]
	ui_text += "Target: '%s'\n" % target
	ui_text += "Confidence: %.2f\n" % confidence
	ui_text += "Source: %s" % source
	
	# SPRINT 03: [03.5] Recovery Reason anzeigen, wenn vorhanden
	var details = report.get("details", {})
	var recovery_reason = details.get("recovery_reason", "")
	if recovery_reason != "":
		ui_text += "\nRecovery: %s" % recovery_reason
	
	debug_label.text = ui_text

# SPRINT 03: [03.5] Zusätzliche Hilfsmethode für schnelle Statusausgabe
func get_short_status() -> String:
	var final = last_reports.get("final_result", {})
	if final.is_empty():
		return "Keine Daten"
		
	var details = final.get("details", {})
	var recovery = details.get("recovery_needed", false)
	var reason = details.get("recovery_reason", "")
	
	if recovery:
		return "%s '%s' (Recovery: %s)" % [
			final.get("command_type", "?"),
			final.get("target", ""),
			reason
		]
	else:
		return "%s '%s' (Clean)" % [
			final.get("command_type", "?"),
			final.get("target", "")
		]
