extends Control

@onready var time_label = $Panel/VBoxContainer/TimeLabel
@onready var daylight_label = $Panel/VBoxContainer/DaylightLabel
@onready var wetter_label = $Panel/VBoxContainer/WetterLabel
@onready var temperatur_label = $Panel/VBoxContainer/TemperaturLabel
@onready var sonnenzeit_label = $Panel/VBoxContainer/SonnenzeitLabel
@onready var energy_label = $Panel/VBoxContainer/EnergyLabel
@onready var thirst_label = $Panel/VBoxContainer/ThirstLabel
@onready var hunger_label = $Panel/VBoxContainer/HungerLabel
@onready var fire_label = $Panel/VBoxContainer/FireLabel
@onready var debug_label = $PanelDebug/DebugScroll/DebugLabel
@onready var copy_button = $PanelDebug/CopyButton

var status_master: Node = null
var weather_manager: Node = null
var fire_handler: Node = null
var debug_messages: Array[String] = []
var max_debug_messages: int = 3

# SPRINT 03: [03.4] Debug UI Element Status
var has_debug_ui: bool = false
var has_copy_button: bool = false
var has_debug_panel: bool = false

# PHASE 2.7: LUCI-DEBUG-AUSGABE ENTFERNT - JETZT IN DEBUGUI.GD

func _ready():
	# SPRINT 03: [03.4] Safeguards für UI-Elemente
	check_debug_ui_elements()

# SPRINT 03: [03.4] Prüft das Vorhandensein der Debug-UI-Elemente
func check_debug_ui_elements():
	# Prüfe Debug-Panel
	var debug_panel = get_node_or_null("PanelDebug")
	if debug_panel == null:
		push_warning("[StatusUI] WARNUNG: PanelDebug nicht gefunden!")
		has_debug_panel = false
	else:
		has_debug_panel = true
		
	# Prüfe Debug-Label
	debug_label = get_node_or_null("PanelDebug/DebugScroll/DebugLabel")
	if debug_label == null:
		push_warning("[StatusUI] WARNUNG: DebugLabel nicht gefunden!")
		has_debug_ui = false
	else:
		has_debug_ui = true
		# Debug-Label konfigurieren
		debug_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
		debug_label.vertical_alignment = VERTICAL_ALIGNMENT_TOP
		debug_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
		debug_label.clip_contents = true
	
	# Prüfe Copy-Button
	copy_button = get_node_or_null("PanelDebug/CopyButton")
	if copy_button == null:
		push_warning("[StatusUI] WARNUNG: CopyButton nicht gefunden!")
		has_copy_button = false
	else:
		has_copy_button = true
		# Kopier-Button verbinden
		copy_button.pressed.connect(_on_copy_button_pressed)
	
	# Debug-Modus Information
	if has_debug_panel:
		if has_debug_ui and has_copy_button:
			print("[StatusUI] Debug-UI vollständig initialisiert")
		else:
			print("[StatusUI] Debug-UI teilweise initialisiert: Label=" + str(has_debug_ui) + ", Button=" + str(has_copy_button))
	else:
		print("[StatusUI] Debug-UI deaktiviert (PanelDebug nicht gefunden)")

func setup(_status_master: Node, _weather_manager: Node = null, _fire_handler: Node = null) -> void:
	status_master = _status_master
	weather_manager = _weather_manager
	fire_handler = _fire_handler

func _process(_delta: float) -> void:
	if Engine.is_editor_hint() or status_master == null:
		return  # Nur im Spiel aktiv

	# Status-Werte
	if energy_label != null:
		energy_label.text = "🔋 Energie: " + str(status_master.get_energy())
	if hunger_label != null:
		hunger_label.text = "🍖 Hunger: " + str(status_master.get_hunger())
	if thirst_label != null:
		thirst_label.text = "💧 Durst: " + str(status_master.get_thirst())

	# Feuer-Status
	if fire_label != null:
		if fire_handler != null:
			fire_label.text = "🔥 " + fire_handler.get_fire_status()
		else:
			fire_label.text = "🔥 Kein Feuer"

	# Wetter-Werte
	if weather_manager != null:
		if time_label != null:
			time_label.text = "🕐 Zeit: " + weather_manager.get_formatted_time()
		if daylight_label != null:
			daylight_label.text = weather_manager.get_daylight_label()
		if wetter_label != null:
			var condition = weather_manager.get_condition()
			if "Sternklar" in condition:
				wetter_label.text = "⭐ " + condition
			else:
				wetter_label.text = "☀️ " + condition
		if temperatur_label != null:
			temperatur_label.text = "🌡️ " + str(int(weather_manager.get_temperature())) + "°C"
		if sonnenzeit_label != null:
			sonnenzeit_label.text = "☀️ " + weather_manager.get_suntime_label()

func update_debug(message: String) -> void:
	# SPRINT 03: [03.4] Safeguard gegen fehlende Debug-UI
	if not has_debug_ui:
		# Nur in Godot-Konsole ausgeben wenn UI fehlt
		print("[DEBUG] " + message)
		return

	# Zeitstempel hinzufügen (nur Stunden:Minuten)
	var time_dict = Time.get_datetime_dict_from_system()
	var time_str = String("%02d:%02d" % [time_dict.hour, time_dict.minute])
	var timestamped_message = "[" + time_str + "] " + message

	debug_messages.append(timestamped_message)

	# Auch in Godot-Konsole ausgeben
	print("[DEBUG] " + message)

	# Nur die letzten 30 Nachrichten behalten
	if debug_messages.size() > 30:
		debug_messages = debug_messages.slice(-30)

	# Debug-Text zusammenbauen - EINFACH
	var debug_text = ""
	for i in range(debug_messages.size()):
		debug_text += debug_messages[i] + "\n"

	if debug_label:
		debug_label.text = debug_text
		# Scroll zum Ende
		call_deferred("_scroll_debug_to_bottom")

func _scroll_debug_to_bottom() -> void:
	# SPRINT 03: [03.4] Safeguard gegen fehlende Debug-UI
	if not has_debug_panel:
		return
		
	# Debug-ScrollContainer zum Ende scrollen
	var debug_scroll = get_node_or_null("PanelDebug/DebugScroll")
	if debug_scroll and debug_scroll is ScrollContainer:
		var v_scroll = debug_scroll.get_v_scroll_bar()
		if v_scroll:
			debug_scroll.scroll_vertical = int(v_scroll.max_value)

func _on_copy_button_pressed() -> void:
	# SPRINT 03: [03.4] Safeguard gegen fehlende Debug-UI
	if not has_debug_ui or not has_copy_button:
		print("[ERROR] Debug-UI Elemente fehlen für Copy-Funktion!")
		return
		
	# Debug-Text in Zwischenablage kopieren
	print("[DEBUG] Copy Button gedrückt!")
	if debug_label.text.length() == 0:
		print("[ERROR] Debug Label ist leer!")
		return

	print("[DEBUG] Kopiere Text: " + str(debug_label.text.length()) + " Zeichen")
	DisplayServer.clipboard_set(debug_label.text)
	print("[INFO] Debug-Text in Zwischenablage kopiert!")

	# Visuelles Feedback
	copy_button.text = "✓"
	await get_tree().create_timer(1.0).timeout
	copy_button.text = "📋"

# PHASE 2.7: LUCI-DEBUG-AUSGABE ENTFERNT - JETZT IN DEBUGUI.GD
# update_luci_debug() Funktion entfernt - verwende DebugUI.gd stattdessen

