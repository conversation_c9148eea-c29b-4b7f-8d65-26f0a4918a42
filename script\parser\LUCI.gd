# LUCI.gd
# Linguistic Understanding & Command Interpreter – zentrale Parser-Instanz
# Nimmt Texteingabe, strukturiert sie zu Commands und leitet sie an GameMaster weiter

extends Node

class_name LUCI

const CommandTypes = preload("res://script/parser/CommandTypes.gd")

# === KONFIGURIERBARE CONFIDENCE-SCHWELLWERTE ===
# PHASE 2.8: EINDEUTIGE SCHWELLWERTE - Keine 0.7-Konflikte mehr
const TARGET_SUGGESTION_THRESHOLD := 0.75  # Target-Korrektur: > 0.75 = direkt, <= 0.75 = Suggestion
const FUZZY_DIRECT_THRESHOLD := 0.85       # Fuzzy direkt: >= 0.85 = direkt ausführen
const FUZZY_SUGGESTION_THRESHOLD := 0.65   # Fuzzy Suggestion: >= 0.65 = Suggestion, < 0.65 = ablehnen
const CONTEXT_ACCEPTANCE_THRESHOLD := 0.7  # ContextStack: >= 0.7 = direkt, < 0.7 = Suggestion/ablehnen
const CONTEXT_SUGGESTION_THRESHOLD := 0.6  # ContextStack Suggestion: >= 0.6 = Suggestion

# === MINI-DEBUG-TOGGLE FÜR STRUKTURIERTE LOGS ===
const DEBUG_LUCI := true  # Aktiviert kompakte Debug-Zeilen für alle Entscheidungen

# === SUGGESTION-TRACKING FÜR JA/NEIN-ANTWORTEN ===
var pending_suggestion: Dictionary = {}  # Speichert aktuelle Suggestion für Ja/Nein-Verarbeitung

# === PRONOMEN-ERKENNUNG ===
const PRONOUNS := ["das", "dies", "diese", "dieses", "jenes", "jene", "jener", "es", "ihn", "sie", "ihr", "ihm"]

var command_type_matcher: Node = null
var fuzzy_memory: Node = null
var context_stack: Node = null
var collision_resolver: Node = null
var syntax_tagger: Node = null
var alias_resolver: Node = null
var yes_no_resolver: Node = null
var unclear_resolver: Node = null
var debug_mode := true

# PHASE 2.7: ECHTE DEBUG-UI INTEGRATION
var debug_ui: Node = null

# PHASE 2.9: LOGBOOK-REFERENZEN ENTFERNT - Nur FuzzyMemory für Lernen

func _ready():
	# Module automatisch aus Child-Nodes laden
	setup_modules()
	if debug_mode:
		print("[LUCI] Ready and listening...")

func setup_modules():
	"""Lädt alle Module automatisch aus Child-Nodes"""
	command_type_matcher = $CommandTypeMatcher
	fuzzy_memory = $FuzzyMemory
	context_stack = $ContextStack
	collision_resolver = $CollisionResolver
	syntax_tagger = $SyntaxTagger
	alias_resolver = $AliasResolver
	yes_no_resolver = $YesNoResolver
	unclear_resolver = $UnclearResolver
	# PHASE 2.7: Systemweites Logbook über GameMaster-Referenz laden
	# logbook wird später von GameMaster gesetzt

	# Module miteinander verknüpfen
	if syntax_tagger:
		syntax_tagger.set_fuzzy_memory(fuzzy_memory)
		syntax_tagger.set_context_stack(context_stack)

	if collision_resolver:
		collision_resolver.set_context_stack(context_stack)
		collision_resolver.set_fuzzy_memory(fuzzy_memory)

	if yes_no_resolver:
		yes_no_resolver.set_context_stack(context_stack)
		yes_no_resolver.set_fuzzy_memory(fuzzy_memory)

	# Debug aktivieren für Tests
	set_debug(true)
	if fuzzy_memory:
		fuzzy_memory.debug_mode = true
	if context_stack:
		context_stack.debug_mode = true

# PHASE 2.9: set_logbook() ENTFERNT - Keine Logbook-Integration mehr

func set_debug_ui(debug_ui_ref: Node) -> void:
	"""PHASE 2.7: Setzt DebugUI und gibt es an Module weiter"""
	debug_ui = debug_ui_ref
	if context_stack != null:
		context_stack.debug_ui = debug_ui_ref
	if debug_mode:
		print("[LUCI] DebugUI verbunden")

# 💬 LOGBOOK-INTEGRATION: Zeitbegriffe parsen

func parse_time_reference(input: String) -> int:
	"""Erkennt einfache Zeitbegriffe und gibt Spieltag zurück"""
	var lowered = input.to_lower()

	# Direkte Zeitbegriffe
	if "heute" in lowered:
		return get_current_day()
	elif "gestern" in lowered:
		return get_current_day() - 1
	elif "vorgestern" in lowered:
		return get_current_day() - 2

	# Tag-Nummern erkennen
	if "tag" in lowered:
		var regex = RegEx.new()
		regex.compile("tag\\s*(\\d+)")
		var result = regex.search(lowered)
		if result:
			return result.get_string(1).to_int()

	# Fallback: aktueller Tag
	return get_current_day()

# 🔧 Rückgabe formatieren statt Rohdaten - neue Struktur
func format_log_entries(entries: Array) -> String:
	"""Formatiert strukturierte Logbook-Einträge für bessere Lesbarkeit"""
	var text = ""
	for entry in entries:
		var time = entry.get("time", "??:??")
		var summary = entry.get("summary", "Unbekanntes Ereignis")
		text += "- " + summary + " (" + time + ")\n"
	return text

func get_current_day() -> int:
	"""🔧 REPARIERT: Holt aktuellen Spieltag sicher"""
	# Versuche über GameMaster-Referenz auf TimeMaster zuzugreifen
	# FIXME: später dynamisch ersetzen wenn TimeMaster als Autoload verfügbar
	return 1  # Sicherer Fallback

func inject_modules(cmd_matcher, fuzz, context, resolver, tagger):
	"""Legacy-Methode für manuelle Injection (falls benötigt)"""
	command_type_matcher = cmd_matcher
	fuzzy_memory = fuzz
	context_stack = context
	collision_resolver = resolver
	syntax_tagger = tagger

# Hauptmethode für die Verarbeitung von Eingaben
func process_input(input_text: String) -> ParsedCommand:
	"""Zentrale Eingabeverarbeitung mit Clean Pass und Recovery Pass"""
	if debug_mode:
		print("[LUCI] Verarbeite Eingabe: '", input_text, "'")

	# PHASE 1: CLEAN PASS
	var clean_pass_result = run_clean_pass(input_text)
	
	# SPRINT 03: [03.1] Recovery Pass auch bei leerem Target aktivieren
	var needs_recovery = false
	var recovery_reason = ""
	
	# Prüfe, ob CommandType unbekannt ist
	if clean_pass_result.get_type() == CommandTypes.CommandType.UNKNOWN:
		needs_recovery = true
		recovery_reason = "unknown_command_type"
	# ODER CommandType bekannt, aber Target leer und CommandType benötigt Target
	elif clean_pass_result.get_type() != CommandTypes.CommandType.UNKNOWN and clean_pass_result.get_target() == "":
		# Prüfe, ob dieser CommandType ein Target benötigt
		var validation_result = check_required_target(clean_pass_result.get_type(), "")
		if not validation_result["valid"]:
			needs_recovery = true
			recovery_reason = "missing_target"
		if debug_mode:
				print("[LUCI] CommandType erkannt, aber Target fehlt. Aktiviere Recovery Pass.")
	
	# Wenn kein Recovery Pass benötigt wird, gib Clean Pass Ergebnis zurück
	if not needs_recovery:
		if debug_mode:
			print("[LUCI] Clean Pass erfolgreich: ", clean_pass_result.get_debug_string())
		return clean_pass_result

	# PHASE 2: RECOVERY PASS - Bei UNKNOWN oder leerem Target
	var recovery_pass_result = run_recovery_pass(input_text.to_lower(), input_text, clean_pass_result.get_type(), recovery_reason)
	if debug_mode:
		print("[LUCI] Recovery Pass Ergebnis: ", recovery_pass_result.get_debug_string())
	
	# [CursorFix 02.4] Debug-Report für gesamten Parser-Prozess
	if debug_ui != null and debug_ui.has_method("show_confidence_report"):
		var final_report = {
			"input": input_text,
			"phase": "final_result",
			"command_type": get_command_name(recovery_pass_result.get_type()),
			"target": recovery_pass_result.get_target(),
			"confidence": recovery_pass_result.get_confidence(),
			"source": recovery_pass_result.get_source(),
			"details": {
				"clean_pass_result": get_command_name(clean_pass_result.get_type()),
				"recovery_needed": true,
				"recovery_reason": recovery_reason
			}
		}
		debug_ui.show_confidence_report(final_report)
	
	return recovery_pass_result

# SPRINT 02: CLEAN PASS - SyntaxTagger → AliasResolver → CommandTypeMatcher
func run_clean_pass(input_text: String) -> ParsedCommand:
	"""Phase 1: Clean Pass - Versucht direkte Erkennung ohne Fuzzy-Korrektur"""
	print("[LUCI] Clean Pass: Verarbeite '", input_text, "'")
	
	var lowered = input_text.to_lower()
	
	# SCHRITT 1: SyntaxTagger für Strukturanalyse
	var target = ""
	if syntax_tagger != null:
		# Extrahiere Ziel und Struktur
		target = syntax_tagger.extract_target(lowered)
		print("[LUCI] Clean Pass - SyntaxTagger Target: '", target, "'")
		
		# REPAIRED: Combo-Crafting-Erkennung (z.B. "stein + kokosnuss")
		var combo_items = syntax_tagger.extract_combo_items(lowered)
		if combo_items.size() >= 2:
			print("[LUCI] Clean Pass - Combo-Crafting erkannt: ", combo_items)
			return ParsedCommand.new(CommandTypes.CommandType.CRAFT, combo_items, 1.0, "clean_pass_combo_crafting")
	
	# SCHRITT 2: AliasResolver für Synonyme und Kurzformen
	if alias_resolver != null:
		var alias_resolved_input = resolve_aliases_early(lowered)
		if alias_resolved_input != lowered:
			print("[LUCI] Clean Pass - Alias-Auflösung: '", lowered, "' → '", alias_resolved_input, "'")
			lowered = alias_resolved_input
	
	# SCHRITT 3: CommandTypeMatcher für Befehlserkennung
	var command_type = CommandTypes.CommandType.UNKNOWN
	if command_type_matcher != null:
		command_type = command_type_matcher.match_command_type(lowered)
		print("[LUCI] Clean Pass - CommandType: ", command_type)
		
		# Wenn kein direkter Match, versuche semantisches Matching
		if command_type == CommandTypes.CommandType.UNKNOWN:
			command_type = command_type_matcher.get_semantic_command_type(lowered)
			print("[LUCI] Clean Pass - Semantischer CommandType: ", command_type)
	
	# SCHRITT 4: Craft-Detection für "benutze X mit Y"
	if command_type == CommandTypes.CommandType.USE and " mit " in lowered:
		var use_parts = lowered.split(" mit ")
		if use_parts.size() == 2:
			var item1 = ""
			var item2 = ""
			
			# Extrahiere das erste Item (nach "benutze")
			var first_part = use_parts[0].strip_edges()
			var words = first_part.split(" ")
			if words.size() > 1:
				item1 = words[words.size() - 1]  # Letztes Wort nach "benutze"
			
			# Extrahiere das zweite Item (nach "mit")
			item2 = use_parts[1].strip_edges()
			
			# Prüfe, ob beide Items gültig sind und ein Crafting-Rezept vorliegt
			if item1 != "" and item2 != "":
				print("[LUCI] Clean Pass - Potentielles Crafting erkannt: '", item1, "' mit '", item2, "'")
				
				# [CursorFix 02.1] Korrigierter Pfad zum RecipeSystem
				var recipe_system = get_node_or_null("/root/Main/LogicSystem/RecipeSystem")
				if recipe_system == null:
					# Versuche alternativen Pfad
					recipe_system = get_node_or_null("/root/Main/Systems/RecipeSystem")
				
				if recipe_system and recipe_system.has_method("has_combination"):
					print("[LUCI] Clean Pass - RecipeSystem gefunden, prüfe Kombination...")
					if recipe_system.has_combination(item1, item2):
						print("[LUCI] Clean Pass - Crafting-Rezept gefunden für: '", item1, "' + '", item2, "'")
						
						# SPRINT 02: Confidence-Report für Craft-Detection
						if debug_ui != null and debug_ui.has_method("show_confidence_report"):
							var report = {
								"input": input_text,
								"phase": "clean_pass",
								"command_type": "CRAFT",
								"target": item1 + " + " + item2,
								"confidence": 1.0,
								"source": "craft_detection",
								"details": {
									"item1": item1,
									"item2": item2,
									"original_command": "USE"
								}
							}
							debug_ui.show_confidence_report(report)
						
						return ParsedCommand.new(CommandTypes.CommandType.CRAFT, [item1, item2], 1.0, "clean_pass_craft_detection")
					else:
						print("[LUCI] Clean Pass - Keine Kombination für: '", item1, "' + '", item2, "' gefunden")
				else:
					print("[LUCI] Clean Pass - RecipeSystem nicht gefunden oder hat keine has_combination Methode")
	
	# Wenn CommandType erkannt wurde, erstelle ParsedCommand
	if command_type != CommandTypes.CommandType.UNKNOWN:
		# Bei Pronomen als Target, versuche Auflösung
		if target != "" and is_pronoun(target):
			print("[LUCI] Clean Pass - Pronomen als Target erkannt: '", target, "'")
			return handle_pronoun_target(command_type, target)

		# Validiere Command mit Target
		var validated_command = create_validated_command(command_type, target, 1.0, "clean_pass")
		
		# SPRINT 02: Confidence-Report für erfolgreichen Clean Pass
		if debug_ui != null and debug_ui.has_method("show_confidence_report"):
			var report = {
				"input": input_text,
				"phase": "clean_pass",
				"command_type": get_command_name(command_type),
				"target": target,
				"confidence": 1.0,
				"source": "direct_match",
				"details": {
					"resolved_input": lowered,
					"alias_resolved": lowered != input_text.to_lower()
				}
			}
			debug_ui.show_confidence_report(report)
		
		print("[LUCI] Clean Pass - Validiertes Kommando: ", validated_command.get_debug_string())
		return validated_command
	
	# Clean Pass konnte keinen gültigen CommandType erkennen
	print("[LUCI] Clean Pass - Kein CommandType erkannt")
	return ParsedCommand.new(CommandTypes.CommandType.UNKNOWN, "", 0.0, "clean_pass_unknown")

# SPRINT 02: RECOVERY PASS - FuzzyMemory → AliasResolver → CommandTypeMatcher
# SPRINT 03: [03.1] - Erweiterung: Recovery auch bei bekanntem CommandType mit leerem Target
func run_recovery_pass(lowered: String, original_input: String, known_command_type: int = CommandTypes.CommandType.UNKNOWN, recovery_reason: String = "") -> ParsedCommand:
	"""Phase 2: Recovery Pass - Versucht Fuzzy-Korrektur und Kontext-Inferenz"""
	print("[LUCI] Recovery Pass: Verarbeite '", lowered, "'")
	
	# SPRINT 03: [03.1] - Bei bekanntem CommandType mit leerem Target direkt ContextStack abfragen
	if known_command_type != CommandTypes.CommandType.UNKNOWN and recovery_reason == "missing_target":
			if context_stack != null:
				var context_result = context_stack.infer_from_context(lowered)
			
			# Zusätzlich den CommandType mitgeben, damit ContextStack präziser suchen kann
			if context_result.is_empty() and context_stack.has_method("infer_for_command_type"):
				context_result = context_stack.infer_for_command_type(known_command_type)
			
			print("[LUCI] Recovery Pass (missing_target) - ContextStack Ergebnis: ", context_result)
			
				if context_result != null and not context_result.is_empty():
					var context_target = context_result.get("target", "")
					var context_confidence = context_result.get("confidence", 0.0)
				
				if context_target != "" and context_confidence >= CONTEXT_ACCEPTANCE_THRESHOLD:
					# Hohes Vertrauen: Direkte Ausführung
					print("[LUCI] Recovery Pass - Hohe Context-Confidence für fehlendes Target: '", context_target, "'")
					var direct_context_result = create_validated_command(known_command_type, context_target, context_confidence, "recovery_missing_target")
					
					# Confidence-Report für Recovery bei fehlendem Target
					if debug_ui != null and debug_ui.has_method("show_confidence_report"):
						var report = {
							"input": original_input,
							"phase": "recovery_pass",
							"command_type": get_command_name(known_command_type),
							"target": context_target,
							"confidence": context_confidence,
							"source": "context_for_missing_target",
							"details": {
								"known_command": get_command_name(known_command_type),
								"recovery_reason": "missing_target"
							}
						}
						debug_ui.show_confidence_report(report)
					
					return direct_context_result
				elif context_target != "" and context_confidence >= CONTEXT_SUGGESTION_THRESHOLD:
					# Mittlere Confidence: Suggestion anzeigen
					print("[LUCI] Recovery Pass - Mittlere Context-Confidence für fehlendes Target: '", context_target, "'")
					set_pending_suggestion(known_command_type, context_target, context_confidence, "recovery_missing_target_suggestion", original_input)

					# Confidence-Report für Suggestion bei fehlendem Target
					if debug_ui != null and debug_ui.has_method("show_confidence_report"):
						var report = {
							"input": original_input,
							"phase": "recovery_pass",
							"command_type": get_command_name(known_command_type),
							"target": context_target,
							"confidence": context_confidence,
							"source": "suggestion_for_missing_target",
							"details": {
								"known_command": get_command_name(known_command_type),
								"recovery_reason": "missing_target"
							}
						}
						debug_ui.show_confidence_report(report)
					
					if unclear_resolver != null:
						var suggestion_text = "Soll ich " + get_command_name(known_command_type) + " " + context_target + "?"
						unclear_resolver.show_suggestion(suggestion_text, known_command_type, context_target)
					
					return ParsedCommand.new(CommandTypes.CommandType.UNKNOWN, context_target, context_confidence, "recovery_missing_target_suggestion")
				
				# Wenn context_target leer oder confidence zu niedrig ist, fallen wir auf regulären Recovery-Prozess zurück
			}
	}
	
	# SCHRITT 1: FuzzyMemory für gesamten Input
	if fuzzy_memory != null:
		var fuzzy_result = fuzzy_memory.resolve_fuzzy_command(lowered)
		print("[LUCI] Recovery Pass - FuzzyMemory Ergebnis: ", fuzzy_result)

		if fuzzy_result != null and not fuzzy_result.is_empty():
			var fuzzy_confidence = fuzzy_result.get("confidence", 0.0)
			var fuzzy_target = fuzzy_result.get("target", "")
			var fuzzy_type = fuzzy_result.get("type", CommandTypes.CommandType.UNKNOWN)

			print("[LUCI] Recovery Pass - Fuzzy-Match gefunden: type=", fuzzy_type, ", target=", fuzzy_target, ", confidence=", fuzzy_confidence)

			# KONFIGURIERBARE CONFIDENCE-BASIERTE ENTSCHEIDUNG
			if fuzzy_confidence >= FUZZY_DIRECT_THRESHOLD:
				# Hohe Confidence: Direkt ausführen - mit zentraler Validierung
				print("[LUCI] Recovery Pass - Hohe Fuzzy-Confidence (>= %.1f) - direkte Ausführung" % FUZZY_DIRECT_THRESHOLD)
				var direct_fuzzy_result = create_validated_command(fuzzy_type, fuzzy_target, fuzzy_confidence, "recovery_fuzzy_direct")

				# SPRINT 02: Confidence-Report für hohe Fuzzy-Confidence
				if debug_ui != null and debug_ui.has_method("show_confidence_report"):
					var report = {
						"input": original_input,
						"phase": "recovery_pass",
						"command_type": get_command_name(fuzzy_type),
						"target": fuzzy_target,
						"confidence": fuzzy_confidence,
						"source": "fuzzy_direct",
						"details": {
							"threshold": FUZZY_DIRECT_THRESHOLD,
							"fuzzy_source": fuzzy_result.get("source", "unknown"),
							"recovery_reason": recovery_reason
						}
					}
					debug_ui.show_confidence_report(report)
				
				return direct_fuzzy_result
			elif fuzzy_confidence >= FUZZY_SUGGESTION_THRESHOLD:
				# Mittlere Confidence: Suggestion anzeigen
				print("[LUCI] Recovery Pass - Mittlere Fuzzy-Confidence (>= %.1f) - Suggestion aktivieren" % FUZZY_SUGGESTION_THRESHOLD)
				set_pending_suggestion(fuzzy_type, fuzzy_target, fuzzy_confidence, "recovery_fuzzy_suggestion", original_input)
				
				# SPRINT 02: Confidence-Report für mittlere Fuzzy-Confidence
				if debug_ui != null and debug_ui.has_method("show_confidence_report"):
					var report = {
						"input": original_input,
						"phase": "recovery_pass",
						"command_type": get_command_name(fuzzy_type),
						"target": fuzzy_target,
						"confidence": fuzzy_confidence,
						"source": "fuzzy_suggestion",
						"details": {
							"threshold": FUZZY_SUGGESTION_THRESHOLD,
							"fuzzy_source": fuzzy_result.get("source", "unknown"),
							"recovery_reason": recovery_reason
						}
					}
					debug_ui.show_confidence_report(report)
				
				if unclear_resolver != null:
					var suggestion_text = "Meintest du: " + get_command_name(fuzzy_type) + " " + fuzzy_target + "?"
					unclear_resolver.show_suggestion(suggestion_text, fuzzy_type, fuzzy_target)
				return ParsedCommand.new(CommandTypes.CommandType.UNKNOWN, fuzzy_target, fuzzy_confidence, "recovery_fuzzy_suggestion")

	# SCHRITT 2: ContextStack als Fallback
	if context_stack != null:
		var context_result = context_stack.infer_from_context(lowered)
		print("[LUCI] Recovery Pass - ContextStack Ergebnis: ", context_result)
		if context_result != null and not context_result.is_empty():
			var context_confidence = context_result.get("confidence", 0.0)
			var context_target = context_result.get("target", "")
			var context_type = context_result.get("type", CommandTypes.CommandType.UNKNOWN)

			if context_confidence >= CONTEXT_SUGGESTION_THRESHOLD:
				# Mittlere Confidence: Suggestion anzeigen
				print("[LUCI] Recovery Pass - ContextStack mittlere Confidence (>= %.1f) - Rückfrage" % CONTEXT_SUGGESTION_THRESHOLD)
				set_pending_suggestion(context_type, context_target, context_confidence, "recovery_context_suggestion", original_input)
				
				# SPRINT 02: Confidence-Report für ContextStack-Suggestion
				if debug_ui != null and debug_ui.has_method("show_confidence_report"):
					var report = {
						"input": original_input,
						"phase": "recovery_pass",
						"command_type": get_command_name(context_type),
						"target": context_target,
						"confidence": context_confidence,
						"source": "context_suggestion",
						"details": {
							"threshold": CONTEXT_SUGGESTION_THRESHOLD,
							"context_source": "context_stack",
							"recovery_reason": recovery_reason
						}
					}
					debug_ui.show_confidence_report(report)
				
				if unclear_resolver != null:
					var suggestion_text = "Meintest du: " + get_command_name(context_type) + " " + context_target + "?"
					unclear_resolver.show_suggestion(suggestion_text, context_type, context_target)
				return ParsedCommand.new(CommandTypes.CommandType.UNKNOWN, context_target, context_confidence, "recovery_context_suggestion")
	
	# Recovery Pass konnte keine Korrektur finden
	print("[LUCI] Recovery Pass - Keine Korrektur möglich")
	return ParsedCommand.new(CommandTypes.CommandType.UNKNOWN, "", 0.0, "recovery_pass_failed")

# === TARGET-PFLICHTPRÜFUNG ===

func check_required_target(command_type: int, target1: String) -> Dictionary:
	"""Robuste Validierung, ob ein gültiges Target vorhanden ist – bei allen kritischen Commands"""
	# Liste von Commands, die zwingend ein Ziel brauchen
	var commands_with_required_target = [
		CommandTypes.CommandType.TAKE,
		CommandTypes.CommandType.USE,
		CommandTypes.CommandType.MOVE,
		CommandTypes.CommandType.CRAFT,
		CommandTypes.CommandType.EAT,
		CommandTypes.CommandType.DRINK
	]

	if command_type in commands_with_required_target:
		if target1.strip_edges() == "":
			return {
				"valid": false,
				"error": "Kein Ziel angegeben",
				"suggestion_type": "ask_target",
				"message": "Was genau soll ich %s?" % get_command_name(command_type),
				"command_type": command_type
			}

	return { "valid": true }

func create_validated_command(command_type: int, target: String, confidence: float, origin: String) -> ParsedCommand:
	"""Zentrale Helper-Funktion: Erstellt ParsedCommand mit automatischer Target-Validierung"""
	# Target-Missing-Safeguard für alle kritischen CommandTypes
	var validation_result = check_required_target(command_type, target)
	if not validation_result["valid"]:
		if debug_mode:
			print("[LUCI] Target-Validation Failed – Fallback für CommandType %s" % str(command_type))
		return ParsedCommand.new(CommandTypes.CommandType.UNKNOWN, "", 0.0, "target_missing")

	# USE interpreted as placeholder – not executable
	if command_type == CommandTypes.CommandType.USE:
		if debug_mode:
			print("[LUCI] USE-Command als Platzhalter erkannt: ", target)
		return ParsedCommand.new(CommandTypes.CommandType.UNKNOWN, target, confidence, "use_placeholder")

	return ParsedCommand.new(command_type, target, confidence, origin)

# === MINI-DEBUG-TOGGLE: STRUKTURIERTE LOG-ZEILEN ===

func _log_decision(result: ParsedCommand, context_used: bool, source: String):
	"""Erzeugt kompakte Debug-Zeile für alle wichtigen Entscheidungen"""
	if not DEBUG_LUCI:
		return

	var cmd_name = get_command_name(result.type) if result != null else "NULL"
	var target_str = str(result.target) if result != null and result.target != null else "none"
	var confidence = result.confidence if result != null else 0.0

	print("[LUCI] command=%s, target=%s, confidence=%.2f, source=%s, context_used=%s" % [
		cmd_name, target_str, confidence, source, context_used
	])

	# PHASE 2.7: ECHTE DEBUG-UI INTEGRATION
	if debug_ui != null and debug_ui.has_method("update_luci_decision"):
		debug_ui.update_luci_decision(cmd_name, target_str, confidence, source)

	# PHASE 2.9: LOGBOOK INTEGRATION ENTFERNT - Nur Debug-Ausgaben

func get_command_name(cmd_type: CommandTypes.CommandType) -> String:
	"""Konvertiert CommandType zu lesbarem Namen für Suggestions"""
	match cmd_type:
		CommandTypes.CommandType.TAKE:
			return "nimm"
		CommandTypes.CommandType.LOOK:
			return "schaue"
		CommandTypes.CommandType.MOVE:
			return "gehe"
		CommandTypes.CommandType.EAT:
			return "iss"
		CommandTypes.CommandType.DRINK:
			return "trinke"
		CommandTypes.CommandType.CRAFT:
			return "crafte"
		CommandTypes.CommandType.USE:
			return "verwende"
		CommandTypes.CommandType.FIRE:
			return "feuer"
		CommandTypes.CommandType.HELP:
			return "hilfe"
		CommandTypes.CommandType.INFO:
			return "info"
		CommandTypes.CommandType.TIP:
			return "tipp"
		CommandTypes.CommandType.FIND:
			return "finde"
		CommandTypes.CommandType.SEARCH:
			return "suche"
		CommandTypes.CommandType.INVENTORY:
			return "inventar"
		CommandTypes.CommandType.CLEAR_CONTEXT:
			return "lösche kontext"
		CommandTypes.CommandType.QUERY_LOG:
			return "logbuch abfragen"
		_:
			return "unbekannt"

func set_debug(on: bool):
	debug_mode = on

func get_context_stack():
	"""Gibt ContextStack für GameMaster zurück"""
	if context_stack == null:
		print("[LUCI] WARNUNG: context_stack ist null!")
		return null
	return context_stack

# ENTFERNT: Suggestion-Logic gehört in UnclearResolver, nicht in LUCI!

func has_pending_suggestion() -> bool:
	"""Prüft ob eine Suggestion auf Ja/Nein-Antwort wartet"""
	return not pending_suggestion.is_empty()

func set_pending_suggestion(cmd_type: CommandTypes.CommandType, target: String, confidence: float, source: String, original_input: String = ""):
	"""Speichert eine Suggestion für Ja/Nein-Verarbeitung"""
	# REPARATUR 4: UNKNOWN SICHERHEIT - Blockiere jede Suggestion bei CommandType == 0
	if cmd_type == CommandTypes.CommandType.UNKNOWN:
		print("[LUCI] REPARATUR 4: Suggestion mit CommandType.UNKNOWN blockiert - keine Annahmen bei Unsinn")
		return

	pending_suggestion = {
		"type": cmd_type,
		"target": target,
		"confidence": confidence,
		"source": source,
		"original_input": original_input,  # PHASE 2.7: Für Lernfähigkeit
		"timestamp": Time.get_unix_time_from_system()
	}
	if DEBUG_LUCI:
		print("[LUCI] Suggestion gespeichert: ", get_command_name(cmd_type), " ", target)

	# PHASE 2.7: Debug-UI Integration für Suggestions
	if debug_ui != null and debug_ui.has_method("update_suggestion"):
		var suggestion_text = "Meintest du: " + get_command_name(cmd_type) + " " + target + "?"
		debug_ui.update_suggestion(suggestion_text, original_input)

func clear_pending_suggestion():
	"""Löscht die aktuelle Suggestion"""
	if DEBUG_LUCI and not pending_suggestion.is_empty():
		print("[LUCI] Suggestion gelöscht")
	pending_suggestion.clear()

func get_pending_suggestion() -> Dictionary:
	"""Gibt die aktuelle Suggestion zurück"""
	return pending_suggestion

func resolve_yes_no(input_text: String) -> ParsedCommand:
	"""
	BUGFIX: ROBUSTE Verarbeitung von Ja/Nein-Antworten auf Suggestions
	Gibt ParsedCommand mit suggestion_accepted/suggestion_rejected zurück
	"""
	if not has_pending_suggestion():
		if DEBUG_LUCI:
			print("[LUCI] resolve_yes_no: Keine Suggestion aktiv")
		return null

	var suggestion = get_pending_suggestion()
	var lowered_input = input_text.strip_edges().to_lower()

	# BUGFIX: Validiere Suggestion-Dictionary BEVOR Zugriff
	if not suggestion.has("type") or not suggestion.has("target") or not suggestion.has("confidence"):
		if DEBUG_LUCI:
			print("[LUCI] FEHLER: Ungültige Suggestion-Struktur: ", suggestion)
		clear_pending_suggestion()
		return null

	# Ja-Antworten
	if lowered_input in ["ja", "yes", "ok", "okay", "j", "y"]:
		var cmd_type = suggestion["type"] as CommandTypes.CommandType
		var target = suggestion["target"]
		var confidence = suggestion["confidence"]

		# BUGFIX: Validiere CommandType
		if cmd_type == null:
			if DEBUG_LUCI:
				print("[LUCI] FEHLER: Ungültiger CommandType in Suggestion")
			clear_pending_suggestion()
			return null

		# PHASE 2.9: REPARIERT - Lerne aus bestätigter Suggestion über FuzzyMemory
		if fuzzy_memory != null and suggestion.has("original_input"):
			var original_input = suggestion["original_input"]
			var corrected_command = get_command_name(cmd_type) + " " + target
			fuzzy_memory.learn_from_confirmation(original_input, corrected_command)

			# FEHLER 5: SUGGESTIONS IM LOGBOOK NOTIEREN
			var gm = get_node("/root/Main/GameMaster") if get_node_or_null("/root/Main/GameMaster") != null else null
			if gm != null and gm.has_method("log_suggestion_accepted"):
				gm.log_suggestion_accepted(original_input, corrected_command)

			if DEBUG_LUCI:
				print("[LUCI] Lernvorgang: '", original_input, "' → '", corrected_command, "'")

		clear_pending_suggestion()

		if DEBUG_LUCI:
			print("[LUCI] Ja-Antwort: Suggestion akzeptiert - ", get_command_name(cmd_type), " ", target)

		# BUGFIX: Gib den ursprünglichen CommandType zurück, nicht GREETING
		return create_validated_command(cmd_type, target, confidence, "suggestion_accepted")

	# Nein-Antworten
	elif lowered_input in ["nein", "no", "n"]:
		clear_pending_suggestion()

		if DEBUG_LUCI:
			print("[LUCI] Nein-Antwort: Suggestion abgelehnt")

		return ParsedCommand.new(CommandTypes.CommandType.GREETING, "", 1.0, "suggestion_rejected")

	# Unklare Antwort
	else:
		if DEBUG_LUCI:
			print("[LUCI] resolve_yes_no: Unklare Antwort '", input_text, "' - keine Ja/Nein-Erkennung")
		return null

func is_pronoun(word: String) -> bool:
	"""Prüft ob ein Wort ein Pronomen ist"""
	return word.to_lower().strip_edges() in PRONOUNS

func handle_pronoun_target(command_type: CommandTypes.CommandType, pronoun: String) -> ParsedCommand:
	"""REPARIERT: Behandelt Pronomen als Target - verwendet letztes Item aus ContextStack"""
	if DEBUG_LUCI:
		print("[LUCI] Pronomen '", pronoun, "' erkannt - versuche ContextStack")

	if context_stack != null:
		# PHASE 2.7: GENUS-BASIERTE PRONOMEN-AUFLÖSUNG
		var genus_item = context_stack.get_item_by_pronoun(pronoun)
		if genus_item != "" and genus_item != null:
			# Hohe Confidence für genus-basierte Pronomen-Referenz
			var pronoun_confidence = 0.8
			if DEBUG_LUCI:
				print("[LUCI] Pronomen '", pronoun, "' → Genus-Match: ", genus_item, " (confidence: ", pronoun_confidence, ")")
			return create_validated_command(command_type, genus_item, pronoun_confidence, "pronoun_genus")

		# FALLBACK: Verwende letztes Item direkt aus der Historie
		var last_item = context_stack.get_last_item()
		if last_item != "" and last_item != null:
			# Mittlere Confidence für Fallback auf letztes Item
			var pronoun_confidence = 0.7
			if DEBUG_LUCI:
				print("[LUCI] Pronomen '", pronoun, "' → Fallback letztes Item: ", last_item, " (confidence: ", pronoun_confidence, ")")
			return create_validated_command(command_type, last_item, pronoun_confidence, "pronoun_fallback")
		else:
			# Fallback: Versuche allgemeine Kontext-Inferenz
			var context_result = context_stack.infer_from_context("")
			if context_result != null and not context_result.is_empty():
				var context_target = context_result.get("target", "")
				var context_confidence = context_result.get("confidence", 0.0)
				if context_confidence >= CONTEXT_ACCEPTANCE_THRESHOLD and context_target != "":
					if DEBUG_LUCI:
						print("[LUCI] Pronomen '", pronoun, "' → ContextStack Fallback: ", context_target)
					return create_validated_command(command_type, context_target, context_confidence, "pronoun_context_fallback")

	# Kein passender Kontext - Rückfrage
	if unclear_resolver != null:
		var question = "Was meinst du mit '" + pronoun + "'?"
		unclear_resolver.ask_for_clarity(question)

	return ParsedCommand.new(CommandTypes.CommandType.UNKNOWN, "", 0.0, "pronoun_unclear")

func get_semantic_category(word: String) -> String:
	"""ERWEITERT: Bestimmt semantische Kategorie eines Wortes mit Subkategorien"""
	var edible_items = ["kokosnuss", "kokosnusshälften", "beeren", "fisch", "seetang"]
	var non_edible_items = ["treibholz", "stein", "feuerstein", "holzspäne", "muschel"]
	var known_locations = ["strand", "klippe", "meer", "wald", "höhle", "berg"]
	var known_commands = ["umschauen", "schaue", "gehe", "nimm", "iss", "trinke", "crafte", "verwende"]

	if word in edible_items:
		return "edible_item"
	elif word in non_edible_items:
		return "non_edible_item"
	elif word in known_locations:
		return "location"
	elif word in known_commands:
		return "command"
	else:
		return "unknown"

func is_semantically_valid_for_command(command_type: CommandTypes.CommandType, target: String) -> Dictionary:
	"""Prüft ob Target semantisch für Command gültig ist - gibt Ergebnis mit Grund zurück"""
	var category = get_semantic_category(target)

	match command_type:
		CommandTypes.CommandType.EAT:
			if category == "location":
				return {"valid": false, "reason": target + " ist ein Ort und nicht essbar"}
			elif category == "non_edible_item":
				return {"valid": false, "reason": target + " ist nicht essbar"}
			else:
				return {"valid": true, "reason": ""}
		CommandTypes.CommandType.DRINK:
			if category == "location" and target != "meer":  # Meer könnte trinkbar sein (Salzwasser)
				return {"valid": false, "reason": target + " ist ein Ort und nicht trinkbar"}
			elif category == "non_edible_item":
				return {"valid": false, "reason": target + " ist nicht trinkbar"}
			else:
				return {"valid": true, "reason": ""}
		CommandTypes.CommandType.TAKE:
			# PHASE 2.8: TAKE mit Location → Hinweis auf MOVE
			if category == "location":
				return {"valid": false, "reason": target + " ist ein Ort. Meintest du 'gehe zu " + target + "'?"}
			else:
				return {"valid": true, "reason": ""}
		CommandTypes.CommandType.MOVE:
			if category == "edible_item" or category == "non_edible_item":
				return {"valid": false, "reason": "Du kannst nicht zu " + target + " gehen"}
			else:
				return {"valid": true, "reason": ""}
		CommandTypes.CommandType.CRAFT:
			# CRAFT-Validierung wurde bereits VOR semantischer Prüfung durchgeführt
			return {"valid": true, "reason": ""}
		CommandTypes.CommandType.FIRE:
			# FEHLER 4: FIRE + Target prüfen
			var valid_fire_targets = ["feuer", "großes_feuer", "lagerfeuer"]
			if target.to_lower() not in valid_fire_targets:
				return {"valid": false, "reason": "Du kannst kein Feuer mit " + target + " machen"}
			else:
				return {"valid": true, "reason": ""}
		_:
			# Andere Commands sind flexibel
			return {"valid": true, "reason": ""}

func should_try_fuzzy_for_target(target: String) -> bool:
	"""Prüft ob Target unbekannt ist und Fuzzy-Match versucht werden sollte"""
	var category = get_semantic_category(target)

	# Bekannte Wörter brauchen kein Fuzzy-Match
	if category != "unknown":
		return false

	# Target ist unbekannt, versuche Fuzzy-Match
	return true

# === ALIAS-AUFLÖSUNG: FRÜH UND KONTROLLIERT ===

func resolve_aliases_early(input: String) -> String:
	"""
	Löst Aliase FRÜH im Parser-Flow auf - VOR CommandType-Erkennung
	VORSICHTIG: Nur für definierte Aliase, keine freien Wörter
	"""
	if alias_resolver == null:
		return input

	var words = input.split(" ")
	var resolved_words = []
	var any_changed = false

	for word in words:
		var original_word = word.strip_edges()
		if original_word.is_empty():
			continue

		# Versuche Alias-Auflösung in allen Kategorien
		var resolved_word = try_resolve_word_alias(original_word)

		if resolved_word != original_word:
			if DEBUG_LUCI:
				print("[AliasResolver] '", original_word, "' → '", resolved_word, "'")
			any_changed = true

		resolved_words.append(resolved_word)

	if any_changed:
		return " ".join(resolved_words)
	else:
		return input

func try_resolve_word_alias(word: String) -> String:
	"""
	Versucht ein einzelnes Wort über Aliase aufzulösen
	KONTROLLIERT: Nur definierte Aliase, keine Konkurrenz
	"""
	if alias_resolver == null:
		return word

	var cleaned_word = word.strip_edges().to_lower()

	# Prüfe alle Kategorien in Prioritätsreihenfolge
	var categories = ["command", "item", "location"]
	var found_aliases = []

	for category in categories:
		var resolved = alias_resolver.resolve(cleaned_word, category)
		if resolved != cleaned_word:
			found_aliases.append({"category": category, "resolved": resolved})

	# KEINE KONKURRENZ: Wenn mehrere Aliase gefunden, vorsichtig sein
	if found_aliases.size() > 1:
		if DEBUG_LUCI:
			print("[AliasResolver] WARNUNG: Mehrere Aliase für '", word, "': ", found_aliases)
		# Priorität: command > item > location
		return found_aliases[0]["resolved"]
	elif found_aliases.size() == 1:
		var alias_info = found_aliases[0]
		if DEBUG_LUCI:
			print("[AliasResolver] '", word, "' → '", alias_info["resolved"], "' (", alias_info["category"], "-alias)")
		return alias_info["resolved"]

	# Kein Alias gefunden
	return word

# PATCH 3.1: RÜCKFRAGEN-LOGIK FÜR UNKLARE TARGETS
func prompt_for_missing_command(target: String) -> String:
	"""Erzeugt Rückfrage wenn kein CommandType mit plausibler Target-Kombination gefunden wurde"""
	return "Was möchtest du mit '%s' machen?" % target

func generate_specific_question_for_command(command_type: CommandTypes.CommandType) -> String:
	"""PATCH 3.1: Erzeugt spezifische Rückfragen für Commands ohne Target"""
	match command_type:
		CommandTypes.CommandType.EAT:
			return "Was soll ich essen? (Verfügbare essbare Items werden geprüft)"
		CommandTypes.CommandType.DRINK:
			return "Was soll ich trinken? (Verfügbare trinkbare Items werden geprüft)"
		CommandTypes.CommandType.TAKE:
			return "Was soll ich nehmen? (Verfügbare Items werden geprüft)"
		CommandTypes.CommandType.USE:
			return "Was soll ich verwenden? (Verfügbare Werkzeuge werden geprüft)"
		CommandTypes.CommandType.CRAFT:
			return "Was soll ich herstellen? (Verfügbare Rezepte werden geprüft)"
		CommandTypes.CommandType.MOVE:
			return "Wohin soll ich gehen? (Verfügbare Orte werden geprüft)"
		_:
			return "Was soll ich " + get_command_name(command_type).replace("e", "en") + "?"

func handle_semantic_fallback(target: String, confidence: float) -> ParsedCommand:
	"""PATCH 3.1: Behandelt semantische Fallbacks mit intelligenten Rückfragen oder Suggestions"""
	if DEBUG_LUCI:
		print("[LUCI] Semantischer Fallback für Target: '", target, "' mit Confidence: ", confidence)

	var category = get_semantic_category(target)

	# Bei sehr niedriger Confidence: Offene Rückfrage
	if confidence < 0.6:
		var question = prompt_for_missing_command(target)
		if unclear_resolver != null:
			unclear_resolver.ask_for_clarity(question)
		return ParsedCommand.new(CommandTypes.CommandType.UNKNOWN, target, confidence, "semantic_fallback_question")

	# Bei mittlerer Confidence: Spezifische Suggestions basierend auf Kategorie
	match category:
		"location":
			# Für Orte: Bewegungs-Suggestion
			set_pending_suggestion(CommandTypes.CommandType.MOVE, target, confidence, "semantic_fallback_move", target)
			if unclear_resolver != null:
				var suggestion_text = "Soll ich zu '" + target + "' gehen?"
				unclear_resolver.show_suggestion(suggestion_text, CommandTypes.CommandType.MOVE, target)
			return ParsedCommand.new(CommandTypes.CommandType.UNKNOWN, target, confidence, "semantic_fallback_move")

		"edible_item":
			# Für essbare Items: Mehrere Optionen
			set_pending_suggestion(CommandTypes.CommandType.TAKE, target, confidence, "semantic_fallback_edible", target)
			if unclear_resolver != null:
				var suggestion_text = "Soll ich '" + target + "' nehmen? (Du könntest es auch essen)"
				unclear_resolver.show_suggestion(suggestion_text, CommandTypes.CommandType.TAKE, target)
			return ParsedCommand.new(CommandTypes.CommandType.UNKNOWN, target, confidence, "semantic_fallback_edible")

		"non_edible_item":
			# Für Gegenstände: TAKE-Suggestion
			set_pending_suggestion(CommandTypes.CommandType.TAKE, target, confidence, "semantic_fallback_item", target)
			if unclear_resolver != null:
				var suggestion_text = "Soll ich '" + target + "' nehmen?"
				unclear_resolver.show_suggestion(suggestion_text, CommandTypes.CommandType.TAKE, target)
			return ParsedCommand.new(CommandTypes.CommandType.UNKNOWN, target, confidence, "semantic_fallback_item")

		_:
			# Unbekannte Kategorie: Offene Rückfrage
			var question = prompt_for_missing_command(target)
			if unclear_resolver != null:
				unclear_resolver.ask_for_clarity(question)
			return ParsedCommand.new(CommandTypes.CommandType.UNKNOWN, target, confidence, "semantic_fallback_unknown")
