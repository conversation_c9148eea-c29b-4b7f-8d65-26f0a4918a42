extends Node

class_name DataLoader

# ============================
# 📁 ZENTRALER DATEN-LOADER
# ============================
# Sprint 02: Zentrale Klasse zum Laden von JSON-Dateien
# - items.json
# - recipes.json
# - world.json
# - status_effects.json

# Pfade zu den Datendateien
const ITEMS_PATH := "res://script/data/items.json"
const RECIPES_PATH := "res://script/data/recipes.json"
const WORLD_PATH := "res://script/data/world.json"
const STATUS_EFFECTS_PATH := "res://script/data/status_effects.json"

# Gecachte Daten
var items_data: Dictionary = {}
var recipes_data: Dictionary = {}
var world_data: Dictionary = {}
var status_effects_data: Dictionary = {}

# Debug-Modus
var debug_mode: bool = true

func _ready():
	# Lade alle Daten beim Start
	load_all_data()

func load_all_data() -> void:
	"""Lädt alle Datendateien"""
	items_data = load_data(ITEMS_PATH)
	recipes_data = load_data(RECIPES_PATH)
	world_data = load_data(WORLD_PATH)
	status_effects_data = load_data(STATUS_EFFECTS_PATH)
	
	if debug_mode:
		print("[DataLoader] Alle Daten geladen:")
		print("  - Items: ", items_data.size(), " Einträge")
		print("  - Recipes: ", recipes_data.size(), " Einträge")
		print("  - World: ", world_data.size(), " Einträge")
		print("  - Status Effects: ", status_effects_data.size(), " Einträge")

func load_data(path: String) -> Dictionary:
	"""Lädt Daten aus einer JSON-Datei mit Fehlerbehandlung"""
	if debug_mode:
		print("[DataLoader] Lade Daten von: ", path)
	
	# Prüfe ob Datei existiert
	if not FileAccess.file_exists(path):
		print("[DataLoader] FEHLER: Datei nicht gefunden: ", path)
		return {}
	
	# Versuche Datei zu öffnen
	var file = FileAccess.open(path, FileAccess.READ)
	if file == null:
		var error = FileAccess.get_open_error()
		print("[DataLoader] FEHLER: Kann Datei nicht öffnen: ", path, " (Error: ", error, ")")
		return {}
	
	# Lese JSON-Inhalt
	var json_text = file.get_as_text()
	file.close()
	
	# Parse JSON mit Fehlerbehandlung
	var json = JSON.new()
	var parse_result = json.parse(json_text)
	if parse_result != OK:
		print("[DataLoader] FEHLER: JSON-Parse-Fehler in ", path)
		print("  Zeile ", json.get_error_line(), ": ", json.get_error_message())
		return {}
	
	var data = json.data
	if typeof(data) != TYPE_DICTIONARY:
		print("[DataLoader] FEHLER: JSON-Daten sind kein Dictionary in ", path)
		return {}
	
	if debug_mode:
		print("[DataLoader] Erfolgreich geladen: ", path)
	
	return data

# === GETTER-METHODEN FÜR ANDERE SYSTEME ===

func get_items_data() -> Dictionary:
	"""Gibt alle Item-Daten zurück"""
	return items_data

func get_recipes_data() -> Dictionary:
	"""Gibt alle Rezept-Daten zurück"""
	return recipes_data

func get_world_data() -> Dictionary:
	"""Gibt alle Welt-Daten zurück"""
	return world_data

func get_status_effects_data() -> Dictionary:
	"""Gibt alle Status-Effekt-Daten zurück"""
	return status_effects_data

func get_item(item_id: String) -> Dictionary:
	"""Gibt ein einzelnes Item zurück"""
	return items_data.get(item_id, {})

func get_recipe(recipe_id: String) -> Dictionary:
	"""Gibt ein einzelnes Rezept zurück"""
	return recipes_data.get(recipe_id, {})

func get_location(location_id: String) -> Dictionary:
	"""Gibt einen einzelnen Ort zurück"""
	if world_data.has("locations") and world_data["locations"].has(location_id):
		return world_data["locations"][location_id]
	return {}

func get_status_effect(effect_id: String) -> Dictionary:
	"""Gibt einen einzelnen Status-Effekt zurück"""
	return status_effects_data.get(effect_id, {})

# === VALIDIERUNGS-METHODEN ===

func validate_item_exists(item_id: String) -> bool:
	"""Prüft ob ein Item existiert"""
	return items_data.has(item_id)

func validate_recipe_exists(recipe_id: String) -> bool:
	"""Prüft ob ein Rezept existiert"""
	return recipes_data.has(recipe_id)

func validate_location_exists(location_id: String) -> bool:
	"""Prüft ob ein Ort existiert"""
	return world_data.has("locations") and world_data["locations"].has(location_id)

func validate_status_effect_exists(effect_id: String) -> bool:
	"""Prüft ob ein Status-Effekt existiert"""
	return status_effects_data.has(effect_id)

# === RELOAD-METHODEN FÜR ENTWICKLUNG ===

func reload_data(path: String) -> Dictionary:
	"""Lädt eine bestimmte Datei neu - nützlich für Entwicklung"""
	if path == ITEMS_PATH:
		items_data = load_data(path)
		return items_data
	elif path == RECIPES_PATH:
		recipes_data = load_data(path)
		return recipes_data
	elif path == WORLD_PATH:
		world_data = load_data(path)
		return world_data
	elif path == STATUS_EFFECTS_PATH:
		status_effects_data = load_data(path)
		return status_effects_data
	else:
		print("[DataLoader] FEHLER: Unbekannter Pfad für Reload: ", path)
		return {} 