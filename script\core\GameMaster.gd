extends Node

class_name GameMaster

# Core Systems
@onready var game_state_master = $CoreSystems/GameStateMaster
@onready var status_master = $CoreSystems/Statusmanager
@onready var time_master = $CoreSystems/TimeMaster
@onready var weather_manager = $CoreSystems/Weathermanager
@onready var data_loader = $CoreSystems/DataLoader  # SPRINT 02: DataLoader für zentralen Datenzugriff

# Action Handlers
@onready var move_handler = $ActionHandlers/MoveHandler
@onready var look_handler = $ActionHandlers/LookHandler
@onready var take_handler = $ActionHandlers/TakeHandler
@onready var search_handler = $ActionHandlers/SearchHandler
@onready var eat_handler = $ActionHandlers/EatHandler
@onready var drink_handler = $ActionHandlers/DrinkHandler
@onready var craft_handler = $ActionHandlers/CraftHandler
@onready var use_handler = $ActionHandlers/UseHandler
@onready var fire_handler = $ActionHandlers/FireHandler

# Logic Systems
@onready var inventory_system = $LogicSystem/InventorySystem
@onready var recipe_system = $LogicSystem/RecipeSystem

# Support Systems
@onready var gelber_engel = $Supportsystems/GelberEngel
@onready var aufgaben_system = $Supportsystems/AufgabenSystem
# PHASE 2.9: Logbook-Referenzen entfernt - Logbook ist separates Modul

# Parser Systems - LUCI statt CommandParser
@onready var luci_parser = $ParserSystems/LUCI
# ARCHITEKTUR-REPARATUR: Legacy CommandParser entfernt

# UI
@onready var dialog_ui = $DialogUI
@onready var status_ui = $StatusUI
# PHASE 2.9: DebugUI-Referenzen entfernt - Keine GUI-Änderungen

var world_data: Dictionary
var items_data: Dictionary
# ARCHITEKTUR-REPARATUR: Suggestion-System nur in LUCI, nicht in GameMaster

# Debug-System
var debug_mode: bool = true  # 🔧 DevMode für Logbook-Integration
var last_status_update_time: float = 0.0
var status_update_interval: float = 60.0  # Alle 60 Sekunden Status-Update





func _ready():
	# SPRINT 03: [03.3] Safeguard für fehlenden DataLoader-Knoten
	if $CoreSystems/DataLoader == null:
		push_warning("[GameMaster] WARNUNG: DataLoader-Knoten nicht gefunden! Fallback auf direkte Dateiladung wird verwendet.")
		print("[GameMaster] DEBUG: Pfad zum erwarteten DataLoader: " + str(get_path()) + "/CoreSystems/DataLoader")
	
	load_world()
	load_items()
	setup_handlers()
	start_game()

func _process(delta: float) -> void:
	# Regelmäßige Status-Updates
	last_status_update_time += delta
	if last_status_update_time >= status_update_interval:
		var temperature = weather_manager.get_temperature()
		status_master.advance_time_minutes(1.0, temperature)  # 1 Minute pro Update
		# Kein Debug-Log für regelmäßige Updates
		last_status_update_time = 0.0

# SPRINT 03: [03.3] Hilfsfunktion zum sicheren Abrufen von Knoten
func get_node_safely(path: NodePath) -> Node:
	"""Gibt den angeforderten Knoten zurück oder null, mit einer Warnung wenn nicht gefunden"""
	var node = get_node_or_null(path)
	if node == null:
		push_warning("[GameMaster] WARNUNG: Knoten nicht gefunden: " + str(path))
	return node

func load_world():
	# SPRINT 03: [03.3] Erweiterte Fehlerbehandlung beim Laden von Daten
	var data_loaded = false
	
	# SPRINT 02: Verwende DataLoader statt direktem Dateizugriff
	data_loader = get_node_safely("CoreSystems/DataLoader")
	if data_loader != null and data_loader.has_method("get_world_data"):
		world_data = data_loader.get_world_data()
		if not world_data.is_empty():
			print("[GameMaster] World-Daten über DataLoader geladen")
			data_loaded = true
		else:
			push_warning("[GameMaster] DataLoader lieferte leere World-Daten!")
	
	if not data_loaded:
		# Fallback: Direkter Dateizugriff
	var path := "res://script/data/world.json"
	if not FileAccess.file_exists(path):
		push_error("[ERROR] world.json nicht gefunden bei: " + path)
			# Notfall-Ersatz: Minimales World-Objekt
			world_data = {
				"start_location": "strand",
				"locations": {
					"strand": {
						"name": "Strand",
						"description": "Du stehst an einem verlassenen Strand. Das System konnte die Welt-Daten nicht laden.",
						"exits": {},
						"items": []
					}
				}
			}
		return
		
	var file = FileAccess.open(path, FileAccess.READ)
		var content = file.get_as_text()
		var parse_result = JSON.parse_string(content)
		
		if parse_result != null:
			world_data = parse_result
			print("[INFO] world.json erfolgreich direkt geladen!")
		else:
			push_error("[ERROR] Fehlerhaftes JSON-Format in world.json!")
			# Notfall-Ersatz wie oben
			world_data = {
				"start_location": "strand",
				"locations": {
					"strand": {
						"name": "Strand",
						"description": "Du stehst an einem verlassenen Strand. Das System konnte die Welt-Daten nicht laden.",
						"exits": {},
						"items": []
					}
				}
			}

func load_items():
	# SPRINT 03: [03.3] Erweiterte Fehlerbehandlung beim Laden von Daten
	var data_loaded = false
	
	# SPRINT 02: Verwende DataLoader statt direktem Dateizugriff
	data_loader = get_node_safely("CoreSystems/DataLoader")
	if data_loader != null and data_loader.has_method("get_items_data"):
		items_data = data_loader.get_items_data()
		if not items_data.is_empty():
			print("[GameMaster] Item-Daten über DataLoader geladen")
			data_loaded = true
		else:
			push_warning("[GameMaster] DataLoader lieferte leere Item-Daten!")
	
	if not data_loaded:
		# Fallback: Direkter Dateizugriff
	var path := "res://script/data/items.json"
	if not FileAccess.file_exists(path):
		push_error("[ERROR] items.json nicht gefunden bei: " + path)
			# Notfall-Ersatz: Minimales Items-Objekt
			items_data = {
				"treibholz": {
					"name": "Treibholz",
					"description": "Ein Stück Treibholz.",
					"properties": ["brennbar"]
				}
			}
		return
			
	var file = FileAccess.open(path, FileAccess.READ)
		var content = file.get_as_text()
		var parse_result = JSON.parse_string(content)
		
		if parse_result != null:
			items_data = parse_result
			print("[INFO] items.json erfolgreich direkt geladen!")
		else:
			push_error("[ERROR] Fehlerhaftes JSON-Format in items.json!")
			# Notfall-Ersatz
			items_data = {
				"treibholz": {
					"name": "Treibholz",
					"description": "Ein Stück Treibholz.",
					"properties": ["brennbar"]
				}
			}

func setup_handlers():
	move_handler.setup(world_data, game_state_master, self)
	look_handler.setup(world_data, game_state_master, inventory_system, null, null, self)
	take_handler.setup(world_data, game_state_master, inventory_system, items_data, self)
	search_handler.setup(world_data, game_state_master, self)
	eat_handler.setup(world_data, game_state_master, inventory_system, status_master, time_master, items_data, self)
	drink_handler.setup(world_data, game_state_master, inventory_system, status_master, time_master, items_data, self)
	craft_handler.setup(inventory_system, recipe_system, time_master, status_master, self, fire_handler)
	use_handler.setup(inventory_system, time_master, status_master, self, fire_handler)
	fire_handler.setup(weather_manager, time_master, status_master, self, inventory_system)
	weather_manager.setup(time_master)
	aufgaben_system.setup(status_master, weather_manager, time_master, inventory_system)
	status_ui.setup(status_master, weather_manager, fire_handler)  # PHASE 2.7: LUCI-Debug entfernt
	dialog_ui.game_master = self

	# 📋 LOGBOOK FINALISIERT: Reset beim Spielstart
	Logbook.reset_logbook()

	# REPARATUR: UnclearResolver mit GameMaster verbinden für sichtbare Suggestions
	if luci_parser:
		var unclear_resolver = luci_parser.get_node_or_null("UnclearResolver")
		if unclear_resolver and unclear_resolver.has_method("set_game_master"):
			unclear_resolver.set_game_master(self)
			debug_log("UnclearResolver mit GameMaster verbunden")

func start_game():
	var start_location = world_data["start_location"]
	game_state_master.set_location(start_location)
	var intro_text = world_data["locations"][start_location]["description"]
	dialog_ui.show_message(intro_text)

func handle_input(text: String):
	# Chat-Verlauf in Godot-Konsole - EINFACH
	print("CHAT INPUT: " + text)
	print("[GAMEMASTER] handle_input aufgerufen mit: '" + text + "'")

	# Sicherheits-Check für leere/problematische Eingaben
	if text.strip_edges().is_empty():
		dialog_ui.show_message("Du nuschelst. Sprich deutlich!")
		return

	# Sehr lange Eingaben abfangen
	if text.length() > 100:
		dialog_ui.show_message("Du redest zu viel auf einmal. Sag es einfacher!")
		return

	# LUCI Parser verwenden mit Fehlerbehandlung
	var parsed_result
	if luci_parser == null:
		print("[GAMEMASTER] FEHLER: LUCI Parser ist null!")
		dialog_ui.show_message("Du nuschelst. Sprich deutlich!")
		return

	# REPARATUR: Prüfe Ja/Nein-Antworten BEVOR normaler Parser-Aufruf
	if luci_parser.has_pending_suggestion():
		var yes_no_result = luci_parser.resolve_yes_no(text)
		if yes_no_result != null:
			# Ja/Nein wurde erkannt - verwende das Ergebnis
			parsed_result = yes_no_result
			print("[GAMEMASTER] Ja/Nein-Antwort verarbeitet: " + str(parsed_result.origin))
		else:
			# Kein Ja/Nein erkannt - normaler Parser-Aufruf
			print("[GAMEMASTER] Rufe LUCI auf mit: '" + text + "'")
			# [CursorFix 02.4] Verwende process_input statt parse_input für DebugUI-Integration
			if luci_parser.has_method("process_input"):
				parsed_result = luci_parser.process_input(text)
			else:
			parsed_result = luci_parser.parse_input(text)
	else:
		# BUGFIX: Behandle "ja" ohne Suggestion
		var lowered_text = text.strip_edges().to_lower()
		if lowered_text in ["ja", "yes", "ok", "okay", "j", "y"]:
			print("[GAMEMASTER] 'Ja' ohne aktive Suggestion")
			dialog_ui.show_message("Ich weiß nicht, worauf du dich beziehst.")
			return
		# Keine Suggestion pending - normaler Parser-Aufruf
		print("[GAMEMASTER] Rufe LUCI auf mit: '" + text + "'")
		# [CursorFix 02.4] Verwende process_input statt parse_input für DebugUI-Integration
		if luci_parser.has_method("process_input"):
			parsed_result = luci_parser.process_input(text)
		else:
		parsed_result = luci_parser.parse_input(text)

	if parsed_result == null:
		print("[GAMEMASTER] FEHLER: LUCI gab null zurück!")
		dialog_ui.show_message("Du nuschelst. Sprich deutlich!")
		return

	# ARCHITEKTUR-REPARATUR: Suggestion-System nur in LUCI verwalten

	# CRASH-SCHUTZ: Sichere String-Konvertierung für Arrays
	var target_str = str(parsed_result.target) if parsed_result.target != null else "null"
	print("[GAMEMASTER] LUCI Ergebnis: " + str(parsed_result.type) + ", Target: " + target_str + ", Confidence: " + str(parsed_result.confidence))

	# BUGFIX: Behandle suggestion_accepted für alle CommandTypes
	if parsed_result.origin == "suggestion_accepted":
		debug_log("LUCI-Suggestion akzeptiert: " + str(parsed_result.type) + " " + str(parsed_result.target))

	# SAFEGUARD: Nur erlaubte CommandTypes weiterleiten
	var known_handlers = [
		CommandTypes.CommandType.LOOK,
		CommandTypes.CommandType.MOVE,
		CommandTypes.CommandType.TAKE,
		CommandTypes.CommandType.EAT,
		CommandTypes.CommandType.DRINK,
		CommandTypes.CommandType.CRAFT,
		CommandTypes.CommandType.SEARCH,
		CommandTypes.CommandType.FIRE,
		CommandTypes.CommandType.GREETING,
		CommandTypes.CommandType.QUERY_LOG,
		CommandTypes.CommandType.HELP,
		CommandTypes.CommandType.INFO,
		CommandTypes.CommandType.TIP,
		CommandTypes.CommandType.INVENTORY,
		CommandTypes.CommandType.RECIPES,
		CommandTypes.CommandType.LOCATION,
		CommandTypes.CommandType.FIND
	]

	if not parsed_result.type in known_handlers:
		print("[DEBUG] GameMaster: Unbekannter oder nicht unterstützter CommandType: " + str(parsed_result.type))

		# Zugriff auf UnclearResolver über LUCI-Parser
		var unclear_resolver = null
		if luci_parser:
			unclear_resolver = luci_parser.get_node_or_null("UnclearResolver")

		if parsed_result.origin == "use_placeholder":
			if unclear_resolver != null and unclear_resolver.has_method("resolve_use_placeholder"):
				unclear_resolver.resolve_use_placeholder(parsed_result)
			else:
				dialog_ui.show_message("Was möchtest du mit " + str(parsed_result.target) + " tun?")
		else:
			if unclear_resolver != null and unclear_resolver.has_method("resolve_target_missing"):
				unclear_resolver.resolve_target_missing(parsed_result)
			else:
				dialog_ui.show_message("Unbekannter Befehl.")

		return

	# Befehl ausführen basierend auf LUCI-Ergebnis
	match parsed_result.type:
		CommandTypes.CommandType.TAKE:
			# CRASH-SCHUTZ: Target sollte String sein für TAKE
			var take_target = str(parsed_result.target) if parsed_result.target != null else ""
			var result = take_handler.take_item(take_target)
			# Zeit/Energie nur bei Erfolg verbrauchen
			if not result.begins_with("Hier gibt es kein"):
				time_master.add_minutes(2.0)
				var temperature = weather_manager.get_temperature()
				status_master.advance_time_minutes(2.0, temperature)
				status_master.change_energy(-2)
				debug_log("Item aufnehmen: " + take_target + ", Zeit +2min, Energie -2")

				# VORGABE 4: Redundanz-Check entfernt - Logbook dokumentiert alles
				var current_day = 1
				var current_time = "08:00"

				if time_master != null and time_master.has_method("get_day"):
					current_day = time_master.get_day()
				if time_master != null and time_master.has_method("get_time_string"):
					current_time = time_master.get_time_string()

				Logbook.add_structured_entry(current_day, current_time, "item_taken", take_target, take_target + " aufgenommen", ["item", "take", "inventory"])

				print("[GameMaster] Logbook-Eintrag erstellt: Tag ", current_day, ", ", current_time, " - ", take_target)

				# ContextStack füllen bei Erfolg
				if luci_parser and luci_parser.has_method("get_context_stack"):
					var context_stack = luci_parser.get_context_stack()
					if context_stack and context_stack.has_method("push_context"):
						context_stack.push_context("item", take_target)
						context_stack.push_context("action", "nimm")
				# Logbook: Erfolgreiche Aktion
				log_successful_action(parsed_result)
			else:
				debug_log("Item nicht gefunden: " + take_target + " (keine Zeit/Energie verbraucht)")
			dialog_ui.show_message(result)
		CommandTypes.CommandType.LOOK:
			var result = look_handler.look()
			# Zeit/Energie für Umschauen
			time_master.add_minutes(1.0)
			var temperature = weather_manager.get_temperature()
			status_master.advance_time_minutes(1.0, temperature)
			status_master.change_energy(-0.5)
			debug_log("Schauen: " + game_state_master.current_location + ", Zeit +1min, Energie -0.5")
			# ContextStack füllen bei LOOK
			if luci_parser and luci_parser.has_method("get_context_stack"):
				var context_stack = luci_parser.get_context_stack()
				if context_stack and context_stack.has_method("push_context"):
					context_stack.push_context("location", game_state_master.current_location)
					context_stack.push_context("action", "schaue")
			dialog_ui.show_message(result)
		CommandTypes.CommandType.MOVE:
			# CRASH-SCHUTZ: Target sollte String sein für MOVE
			var move_target = str(parsed_result.target) if parsed_result.target != null else ""

			# REPARATUR 4: MOVE MIT LEEREM ZIEL ABFANGEN
			if move_target.is_empty() or move_target == "null":
				var context_question = generate_move_context_question()
				dialog_ui.show_message(context_question)
				return

			var previous_location = game_state_master.current_location
			var result = move_handler.move_to(move_target)
			var new_location = game_state_master.current_location

			# REPARATUR 1: MOVE-EVENTS INS LOGBOOK
			if previous_location != new_location:
				log_move(previous_location, new_location)

			# Zeit/Energie für Bewegung nur bei erfolgreicher Bewegung
			if previous_location != new_location:
				time_master.add_minutes(10.0)
				var temperature = weather_manager.get_temperature()
				status_master.advance_time_minutes(10.0, temperature)
				status_master.change_energy(-5)
				debug_log("Bewegung: " + move_target + ", Zeit +10min, Energie -5")
				# ContextStack füllen bei Bewegung
				if luci_parser and luci_parser.has_method("get_context_stack"):
					var context_stack = luci_parser.get_context_stack()
					if context_stack and context_stack.has_method("push_context"):
						context_stack.push_context("location", move_target)
						context_stack.push_context("action", "gehe")

			dialog_ui.show_message(result)
		CommandTypes.CommandType.SEARCH:
			# CRASH-SCHUTZ: Target sollte String sein für SEARCH
			var search_target = str(parsed_result.target) if parsed_result.target != null else ""
			var result = search_handler.search_for_item(search_target)
			dialog_ui.show_message(result)
		CommandTypes.CommandType.CRAFT:
			var target = parsed_result.target

			# NEUER ANSATZ: Prüfe ob target ein Array ist (von LUCI Combo-Crafting)
			if target is Array:
				if target.size() >= 2:
					var item1 = str(target[0])  # SICHER zu String konvertieren
					var item2 = str(target[1])  # SICHER zu String konvertieren
					debug_log("Combo-Crafting: " + item1 + " + " + item2)
					var result = craft_handler.craft_combo(item1, item2)

					# PROBLEM 4: LOGBOOK-TYPE FÜR BESONDERES CRAFTING (FEUER)
					if result.begins_with("Du stellst") or result.begins_with("Du machst"):
						log_craft_success("crafted_item", [item1, item2], 1)
					elif result.begins_with("Das Feuer"):
						log_fire_started("combo_feuer")
					elif result.begins_with("Du hast nicht") or result.begins_with("Unbekanntes Rezept"):
						log_craft_fail([item1, item2])

					dialog_ui.show_message(result)
				else:
					debug_log("Ungültiges Array-Format: " + str(target))
					dialog_ui.show_message("Ungültiges Crafting-Format.")
			# LEGACY: String-basiertes Combo-Crafting (für Rückwärtskompatibilität)
			elif target is String and "+" in target:
				var parts = target.split("+")
				if parts.size() >= 2:
					var item1 = parts[0].strip_edges()
					var item2 = parts[1].strip_edges()
					debug_log("Combo-Crafting (String): " + item1 + " + " + item2)
					var result = craft_handler.craft_combo(item1, item2)

					# FEHLER 1: FALSCHER EINTRAG BEI CRAFT FEUER BEHOBEN
					if result.begins_with("Du stellst") or result.begins_with("Du machst") or result.begins_with("Das Feuer"):
						log_craft_success("crafted_item", [item1, item2], 1)
					elif result.begins_with("Du hast nicht") or result.begins_with("Unbekanntes Rezept"):
						log_craft_fail([item1, item2])

					dialog_ui.show_message(result)
				else:
					debug_log("Ungültiges Combo-Format: " + target)
					dialog_ui.show_message("Ungültiges Crafting-Format. Verwende: item1 + item2")
			# STANDARD: Einzelnes Item crafting
			else:
				var single_target = str(target)  # Sicher zu String konvertieren
				var craft_result = craft_handler.craft_item(single_target)
				debug_log("Crafting: " + single_target)

				# STRUKTURIERTE HANDLER-RESULT VERARBEITUNG
				if craft_result.has("success") and craft_result["success"]:
					# ERFOLG: Logbook-Eintrag und Message
					if craft_result.has("log"):
						log_craft_success(single_target, [single_target], 1)
					dialog_ui.show_message(craft_result["message"])
				else:
					# FEHLER: Message anzeigen, optional Debug an GelberEngel
					if craft_result.has("missing_items") and not craft_result["missing_items"].is_empty():
						log_craft_fail([single_target])
					dialog_ui.show_message(craft_result["message"])
		CommandTypes.CommandType.FIRE:
			# CRASH-SCHUTZ: Target sollte String sein für FIRE
			var fire_target = str(parsed_result.target) if parsed_result.target != null else ""
			var result = fire_handler.add_fuel(fire_target)
			dialog_ui.show_message(result)
		CommandTypes.CommandType.EAT:
			# CRASH-SCHUTZ: Target sollte String sein für EAT
			var eat_target = str(parsed_result.target) if parsed_result.target != null else ""

			# REPARATUR 4: FEHLVERSUCH BEI "NICHT IM INVENTAR"
			if not inventory_system.has_item(eat_target):
				log_inventory_fail("iss", eat_target)

			var result = eat_handler.eat_item(eat_target)
			# ContextStack füllen bei EAT
			if result.begins_with("Du isst") and luci_parser and luci_parser.has_method("get_context_stack"):
				var context_stack = luci_parser.get_context_stack()
				if context_stack and context_stack.has_method("push_context"):
					context_stack.push_context("item", eat_target)
					context_stack.push_context("action", "iss")
			dialog_ui.show_message(result)
		CommandTypes.CommandType.DRINK:
			# CRASH-SCHUTZ: Target sollte String sein für DRINK
			var drink_target = str(parsed_result.target) if parsed_result.target != null else ""

			# REPARATUR 4: FEHLVERSUCH BEI "NICHT IM INVENTAR"
			if not inventory_system.has_item(drink_target):
				log_inventory_fail("trinke", drink_target)

			var result = drink_handler.drink_item(drink_target)
			# ContextStack füllen bei DRINK
			if result.begins_with("Du trinkst") and luci_parser and luci_parser.has_method("get_context_stack"):
				var context_stack = luci_parser.get_context_stack()
				if context_stack and context_stack.has_method("push_context"):
					context_stack.push_context("item", drink_target)
					context_stack.push_context("action", "trinke")
			dialog_ui.show_message(result)
		CommandTypes.CommandType.GREETING:
			# ARCHITEKTUR-REPARATUR: Ja/Nein-Verarbeitung komplett über LUCI
			if parsed_result.origin == "suggestion_rejected":
				debug_log("LUCI-Suggestion abgelehnt")
				dialog_ui.show_message("Okay, verstanden.")
			else:
				dialog_ui.show_message("Hallo! Wie kann ich dir helfen?")
		CommandTypes.CommandType.QUERY_LOG:
			# 🧠 LOGBOOK-INTEGRATION: Logbuch-Abfragen
			handle_query_log(parsed_result)
		CommandTypes.CommandType.HELP:
			# CRASH-SCHUTZ: Target sollte String sein für HELP
			var help_target = str(parsed_result.target) if parsed_result.target != null else ""
			var result = gelber_engel.get_hilfe(help_target)
			debug_log("Gelber Engel Hilfe: " + help_target)
			dialog_ui.show_message(result)
		CommandTypes.CommandType.INFO:
			# CRASH-SCHUTZ: Target sollte String sein für INFO
			var info_target = str(parsed_result.target) if parsed_result.target != null else ""
			var result = gelber_engel.get_info(info_target)
			debug_log("Gelber Engel Info: " + info_target)
			dialog_ui.show_message(result)
		CommandTypes.CommandType.TIP:
			# CRASH-SCHUTZ: Target sollte String sein für TIP
			var tip_target = str(parsed_result.target) if parsed_result.target != null else ""
			var result = gelber_engel.get_tipp(tip_target)
			debug_log("Gelber Engel Tipp: " + tip_target)
			dialog_ui.show_message(result)
		CommandTypes.CommandType.INVENTORY:
			var items = inventory_system.list_items()
			if items.is_empty():
				dialog_ui.show_message("Du hast nichts dabei.")
			else:
				var items_str = ", ".join(items)
				dialog_ui.show_message("Du trägst: " + items_str + ".")
		CommandTypes.CommandType.RECIPES:
			var result = craft_handler.list_recipes()
			dialog_ui.show_message(result)
		CommandTypes.CommandType.LOCATION:
			var current_loc = game_state_master.current_location
			var location_name = world_data["locations"][current_loc]["name"]
			debug_log("Position abgefragt: " + current_loc)
			dialog_ui.show_message("Du befindest dich: " + location_name + " (" + current_loc + ")")
		CommandTypes.CommandType.FIND:
			# CRASH-SCHUTZ: Target sollte String sein für FIND
			var item_name = str(parsed_result.target) if parsed_result.target != null else ""
			var locations_with_item = []
			# Durchsuche alle Locations nach dem Item
			for location_key in world_data["locations"]:
				var location = world_data["locations"][location_key]
				if location.has("items") and item_name in location["items"]:
					locations_with_item.append(location["name"])

			if locations_with_item.size() > 0:
				var result = item_name + " findest du hier: " + ", ".join(locations_with_item)
				debug_log("Item-Suche: " + item_name + " → " + str(locations_with_item))
				dialog_ui.show_message(result)
			else:
				debug_log("Item-Suche: " + item_name + " → nicht gefunden")
				dialog_ui.show_message("Ich weiß nicht, wo du " + item_name + " finden kannst.")
		_:
			# ARCHITEKTUR-REPARATUR: Suggestion-System nur in LUCI
			# LUCI hat bereits alle Suggestions verarbeitet
			dialog_ui.show_message("Das habe ich nicht verstanden.")

	# Update DebugUI nach jeder Aktion
	if status_ui and status_ui.has_method("update_debug_output"):
		status_ui.update_debug_output()

func handle_look_command():
	# Sicherheits-Check
	if look_handler == null:
		dialog_ui.show_message("Du nuschelst. Sprich deutlich!")
		return

	# Zeit verbrauchen (1 Minute zum Schauen)
	time_master.add_minutes(1.0)
	var temperature = weather_manager.get_temperature()
	status_master.advance_time_minutes(1.0, temperature)
	# Energie verbrauchen (Umschauen ist leichte Bewegung)
	status_master.change_energy(-0.5)
	debug_log("Schauen: Zeit +1min, Energie -0.5")

	var output = look_handler.look()
	# Kontext für LUCI aktualisieren
	if luci_parser != null and luci_parser.context_stack != null:
		luci_parser.context_stack.push_output(output)
	dialog_ui.show_message(output)

func handle_inventory_command():
	var items = inventory_system.list_items()
	if items.is_empty():
		dialog_ui.show_message("Du hast nichts dabei.")
	else:
		var items_str := ""
		for i in range(items.size()):
			if i > 0:
				items_str += ", "
			items_str += str(items[i])
		dialog_ui.show_message("Du trägst: " + items_str + ".")

func handle_take_command(item: String):
	if item.is_empty():
		dialog_ui.show_message("Was möchtest du nehmen?")
		return

	# Kontext-Feedback für LUCI
	if luci_parser != null and luci_parser.context_stack != null:
		var last_item = luci_parser.context_stack.get_last("item")
		if not last_item.is_empty() and item == last_item:
			debug_log("Kontext: 'nimm' -> nehme " + item)

	var result = take_handler.take_item(item)

	# Zeit/Energie nur bei Erfolg verbrauchen
	if not result.begins_with("Hier gibt es kein"):
		# Zeit verbrauchen (2 Minuten zum Aufheben)
		time_master.add_minutes(2.0)
		var temperature = weather_manager.get_temperature()
		status_master.advance_time_minutes(2.0, temperature)
		# Energie verbrauchen (Aufheben ist anstrengend)
		status_master.change_energy(-2)
		debug_log("Item aufnehmen: " + item + ", Zeit +2min, Energie -2")
	else:
		debug_log("Item nicht gefunden: " + item + " (keine Zeit/Energie verbraucht)")

	# Kontext für LUCI aktualisieren
	if luci_parser != null and luci_parser.context_stack != null:
		luci_parser.context_stack.push_output(result)
	dialog_ui.show_message(result)

func handle_eat_command(item: String):
	if item.is_empty():
		dialog_ui.show_message("Was möchtest du essen?")
		return
	var result = eat_handler.eat_item(item)
	dialog_ui.show_message(result)

func handle_drink_command(item: String):
	if item.is_empty():
		dialog_ui.show_message("Was möchtest du trinken?")
		return
	var result = drink_handler.drink_item(item)
	dialog_ui.show_message(result)

func handle_use_command(item: String):
	if item.is_empty():
		dialog_ui.show_message("Was möchtest du verwenden?")
		return
	var result = use_handler.use_item(item)
	dialog_ui.show_message(result)

func handle_craft_command(item: String):
	debug_log("🔥 CRAFT COMMAND: '" + item + "'")
	if item.is_empty():
		dialog_ui.show_message("Was möchtest du craften?")
		return
	var craft_result = craft_handler.craft_item(item)

	# STRUKTURIERTE HANDLER-RESULT VERARBEITUNG
	if craft_result.has("message"):
		dialog_ui.show_message(craft_result["message"])
	else:
		dialog_ui.show_message("Unbekannter Crafting-Fehler.")

func handle_recipes_command():
	dialog_ui.show_message(craft_handler.list_recipes())

func handle_fire_command(item: String):
	if item.is_empty():
		var status = fire_handler.get_fire_status()
		dialog_ui.show_message("Feuer-Status: " + status)
	else:
		var result = fire_handler.add_fuel(item)
		dialog_ui.show_message(result)

func handle_info_command(thema: String):
	var antwort = gelber_engel.get_info(thema)
	debug_log("Gelber Engel Info: " + (thema if not thema.is_empty() else "alle Themen"))
	dialog_ui.show_message(antwort)

func handle_help_command(thema: String):
	var antwort = gelber_engel.get_hilfe(thema)
	debug_log("Gelber Engel Hilfe: " + (thema if not thema.is_empty() else "alle Themen"))
	dialog_ui.show_message(antwort)

func handle_tip_command(thema: String):
	var antwort = gelber_engel.get_tipp(thema)
	debug_log("Gelber Engel Tipp: " + (thema if not thema.is_empty() else "zufällig"))
	dialog_ui.show_message(antwort)

func handle_tasks_command():
	var aufgaben_text = aufgaben_system.get_aufgaben_text()
	debug_log("Aufgaben abgerufen")
	dialog_ui.show_message(aufgaben_text)

func handle_move_command(target: String):
	if target.is_empty():
		dialog_ui.show_message("Wohin möchtest du gehen?")
		return
	# Zeit verbrauchen (10 Minuten zum Gehen)
	time_master.add_minutes(10.0)
	var temperature = weather_manager.get_temperature()
	status_master.advance_time_minutes(10.0, temperature)
	# Energie verbrauchen (Gehen ist anstrengend)
	status_master.change_energy(-5)
	debug_log("Bewegung: " + target + ", Zeit +10min, Energie -5")
	dialog_ui.show_message(move_handler.move_to(target))

func handle_search_command(target: String):
	if target.is_empty():
		# Allgemeine Suche
		time_master.add_minutes(5.0)
		var temperature = weather_manager.get_temperature()
		status_master.advance_time_minutes(5.0, temperature)
		debug_log("Allgemeine Suche, Zeit +5min")
		dialog_ui.show_message(search_handler.search_general())
	else:
		# Spezifische Item-Suche
		time_master.add_minutes(3.0)
		var temperature = weather_manager.get_temperature()
		status_master.advance_time_minutes(3.0, temperature)
		debug_log("Suchen: " + target + ", Zeit +3min")
		dialog_ui.show_message(search_handler.search_for_item(target))

func handle_greeting_command():
	var greetings = [
		"Hallo! Wie kann ich dir helfen?",
		"Hi! Schön dich zu sehen!",
		"Guten Tag! Was kann ich für dich tun?",
		"Hallo! Brauchst du Hilfe? Sag 'hilfe' für Befehle."
	]
	var random_greeting = greetings[randi() % greetings.size()]
	dialog_ui.show_message(random_greeting)

# 🧠 LOGBOOK-INTEGRATION: Logbuch-Abfragen

func handle_query_log(parsed_result: ParsedCommand) -> void:
	"""🔄 SPIELFLUSS: Holt passende Logbook-Einträge und zeigt sie an"""
	var input = str(parsed_result.target) if parsed_result.target != null else ""

	# Zeitbegriff parsen über LUCI
	var day = luci_parser.parse_time_reference(input)

	# Logbook-Einträge holen
	var entries = Logbook.get_entries_for_day(day)

	if entries.size() == 0:
		dialog_ui.show_message("An Tag " + str(day) + " ist nichts Besonderes passiert.")
		return

	# 🔧 Formatierte Ausgabe über LUCI
	var formatted_entries = luci_parser.format_log_entries(entries)
	var output = "=== Tag " + str(day) + " ===\n" + formatted_entries

	# An DialogUI weiterleiten
	dialog_ui.show_message(output)

	debug_log("Logbook-Abfrage: Tag " + str(day) + ", " + str(entries.size()) + " Einträge")

# 🔧 DevMode-Unterstützung für Logbook
func is_debug_mode() -> bool:
	"""Gibt Debug-Status für andere Module zurück"""
	return debug_mode

func handle_confirm_command(answer: String):
	# Für jetzt: Einfache Bestätigung
	if answer == "ja" or answer == "yes" or answer == "ok":
		dialog_ui.show_message("Verstanden!")
	elif answer == "nein" or answer == "no":
		dialog_ui.show_message("Okay, dann nicht.")
	else:
		dialog_ui.show_message("Ich verstehe nur 'ja' oder 'nein'.")

# ARCHITEKTUR-REPARATUR: handle_unknown_command entfernt
# Suggestion-System wird komplett von LUCI verwaltet



func get_fire_handler():
	return fire_handler

func get_command_name_for_type(cmd_type: CommandTypes.CommandType) -> String:
	"""Konvertiert CommandType zu ausführbarem Command-Namen"""
	match cmd_type:
		CommandTypes.CommandType.TAKE:
			return "nimm"
		CommandTypes.CommandType.EAT:
			return "iss"
		CommandTypes.CommandType.DRINK:
			return "trinke"
		CommandTypes.CommandType.MOVE:
			return "gehe"
		CommandTypes.CommandType.CRAFT:
			return "crafte"
		CommandTypes.CommandType.USE:
			return "verwende"
		_:
			return "nimm"  # Fallback

func show_suggestion_message(suggestion: String) -> void:
	"""REPARATUR: Zeigt Suggestions sichtbar im Chat an"""
	debug_log("Suggestion angezeigt: " + suggestion)
	dialog_ui.show_message(suggestion)

# REPARATUR 6: HELFERFUNKTIONEN FÜR GAMELOGS
func log_move(_from, to):
	var current_day = 1
	var current_time = "08:00"
	if time_master != null and time_master.has_method("get_day"):
		current_day = time_master.get_day()
	if time_master != null and time_master.has_method("get_time_string"):
		current_time = time_master.get_time_string()
	Logbook.add_structured_entry(current_day, current_time, "move", to, "ort erreicht", ["move", "location"])

func log_craft_success(output, _inputs, _amount = 1):
	var current_day = 1
	var current_time = "08:00"
	if time_master != null and time_master.has_method("get_day"):
		current_day = time_master.get_day()
	if time_master != null and time_master.has_method("get_time_string"):
		current_time = time_master.get_time_string()
	Logbook.add_structured_entry(current_day, current_time, "craft", output, "rezept hergestellt", ["craft", "success"])

func log_craft_fail(inputs):
	var current_day = 1
	var current_time = "08:00"
	if time_master != null and time_master.has_method("get_day"):
		current_day = time_master.get_day()
	if time_master != null and time_master.has_method("get_time_string"):
		current_time = time_master.get_time_string()
	Logbook.add_structured_entry(current_day, current_time, "craft_fail", str(inputs), "rezept unbekannt", ["craft", "fail"])

func log_inventory_fail(_command, item):
	var current_day = 1
	var current_time = "08:00"
	if time_master != null and time_master.has_method("get_day"):
		current_day = time_master.get_day()
	if time_master != null and time_master.has_method("get_time_string"):
		current_time = time_master.get_time_string()
	Logbook.add_structured_entry(current_day, current_time, "fail", item, "fehlversuch: item nicht vorhanden", ["fail", "inventory"])

# FEHLER 5: SUGGESTIONS IM LOGBOOK NOTIEREN
func log_suggestion_accepted(original_input, corrected_command):
	var current_day = 1
	var current_time = "08:00"
	if time_master != null and time_master.has_method("get_day"):
		current_day = time_master.get_day()
	if time_master != null and time_master.has_method("get_time_string"):
		current_time = time_master.get_time_string()
	Logbook.add_structured_entry(current_day, current_time, "suggestion_accepted", corrected_command, "suggestion angenommen: " + original_input + " → " + corrected_command, ["suggestion", "correction"])

# PROBLEM 4: LOGBOOK-TYPE FÜR BESONDERES CRAFTING (FEUER)
func log_fire_started(fire_type):
	var current_day = 1
	var current_time = "08:00"
	if time_master != null and time_master.has_method("get_day"):
		current_day = time_master.get_day()
	if time_master != null and time_master.has_method("get_time_string"):
		current_time = time_master.get_time_string()

	var current_location = "unbekannt"
	if game_state_master != null and game_state_master.has("current_location"):
		current_location = game_state_master.current_location

	Logbook.add_structured_entry(current_day, current_time, "fire_started", fire_type, "feuer entfacht", ["fire", "craft", "location:" + current_location])

# REPARATUR 4: MOVE-KONTEXT-FRAGE GENERIEREN
func generate_move_context_question() -> String:
	"""Generiert kontextuelle Frage für Bewegung ohne Ziel"""
	var available_locations = get_available_exits()

	if available_locations.size() > 0:
		var locations_text = ", ".join(available_locations)
		return "Wohin willst du gehen? (z. B. " + locations_text + ")"
	else:
		return "Wohin willst du gehen? (Schaue dich um, um verfügbare Ausgänge zu sehen)"

func get_available_exits() -> Array:
	"""Gibt verfügbare Ausgänge vom aktuellen Ort zurück"""
	var exits = []

	if not world_data.has("locations"):
		return exits

	var current_location = game_state_master.get_location()
	if world_data["locations"].has(current_location):
		var location_data = world_data["locations"][current_location]
		if location_data.has("exits"):
			exits = location_data["exits"].keys()

	return exits

func has_recipe(recipe_name: String) -> bool:
	"""CRASH-FIX: Prüft ob Rezept existiert - für LUCI CRAFT-Validierung"""
	if recipe_system == null:
		print("[GameMaster] WARNUNG: RecipeSystem nicht verfügbar")
		return false

	return recipe_system.has_recipe(recipe_name)

func debug_log(message: String) -> void:
	# Debug-Ausgabe in Konsole und UI
	print("[GAME] " + message)
	if status_ui and status_ui.has_method("update_debug"):
		status_ui.update_debug(message)

func log_successful_action(parsed_command: ParsedCommand) -> void:
	"""Zentrale Logbook-Funktion für erfolgreiche Commands"""
	if not Logbook or not Logbook.has_method("log_action"):
		return

	var current_day = 1
	var current_time = "08:00"
	if time_master != null and time_master.has_method("get_day"):
		current_day = time_master.get_day()
	if time_master != null and time_master.has_method("get_time_string"):
		current_time = time_master.get_time_string()

	var command_name = get_command_name_for_type(parsed_command.type)
	var target_str = str(parsed_command.target) if parsed_command.target != null else ""
	var action_description = command_name + " " + target_str

	Logbook.log_action(current_day, current_time, command_name, target_str, action_description, parsed_command.confidence, parsed_command.origin)
