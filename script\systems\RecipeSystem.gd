extends Node

class_name RecipeSystem

# ============================
# 📜 REZEPT-VERWALTUNG
# ============================

var recipes: Dictionary = {}
var data_loader = null  # Referenz zum DataLoader

func _ready():
	# Warte einen Frame, um sicherzustellen, dass DataLoader bereit ist
	await get_tree().process_frame
	load_recipes()

func load_recipes():
	# SPRINT 02: Verwende DataLoader statt hardcoded Rezepte
	data_loader = get_node_or_null("/root/Main/CoreSystems/DataLoader")
	
	if data_loader != null:
		recipes = data_loader.get_recipes_data()
		print("[RecipeSystem] Rezepte über DataLoader geladen: " + str(recipes.keys()))
	else:
		print("[RecipeSystem] WARNUNG: DataLoader nicht gefunden, verwende Fallback-Rezepte")
		# Fallback: Hardcoded Rezepte
	recipes = {
		"kokosnusshälften": {
			"ingredients": ["kokosnuss", "stein"],
			"result": "kokosnusshälften",
			"result_count": 2,
			"time_cost": 5,
			"description": "Kokosnuss mit Stein aufschlagen"
		},
		"holzspäne": {
			"ingredients": ["treibholz", "muschel"],
			"result": "holzspäne",
			"result_count": 1,
			"time_cost": 10,
			"description": "Holzspäne mit Muschel abschnitzen"
		},
		"feuer": {
			"ingredients": ["stein", "feuerstein", "holzspäne"],
			"result": "feuer",
			"result_count": 1,
			"time_cost": 15,
			"burn_time": 60.0,
			"description": "Feuer mit Stein, Feuerstein und Holzspänen entzünden"
		},
		"großes_feuer": {
			"ingredients": ["stein", "feuerstein", "holzspäne", "treibholz"],
			"result": "feuer",
			"result_count": 1,
			"time_cost": 20,
			"burn_time": 120.0,
			"description": "Großes Feuer mit extra Treibholz - brennt länger"
		}
	}
		print("[RecipeSystem] Fallback-Rezepte geladen: " + str(recipes.keys()))

func get_recipe(item_name: String) -> Dictionary:
	return recipes.get(item_name, {})

func has_recipe(item_name: String) -> bool:
	return recipes.has(item_name)

# SPRINT 02: Craft-Detection für "benutze X mit Y"
func has_combination(item1: String, item2: String) -> bool:
	"""
	Prüft, ob zwei Items zusammen ein Crafting-Rezept bilden können.
	Wird von LUCI.gd für die Craft-Detection verwendet.
	"""
	print("[RecipeSystem] Prüfe Kombination: ", item1, " + ", item2)
	
	# Prüfe jedes Rezept auf die Kombination der beiden Items
	for recipe_name in recipes.keys():
		var recipe = recipes[recipe_name]
		var ingredients = recipe.get("ingredients", [])
		
		# Prüfe ob beide Items in den Zutaten enthalten sind
		if item1 in ingredients and item2 in ingredients:
			# Für 2-Item-Rezepte ist das ausreichend
			if ingredients.size() == 2:
				print("[RecipeSystem] Kombination gefunden: ", recipe_name)
				return true
			
			# Für Rezepte mit mehr als 2 Items: Prüfe ob die restlichen Zutaten verfügbar sind
			# Dies könnte später durch Inventory-Check erweitert werden
			if ingredients.size() > 2:
				print("[RecipeSystem] Teilweise Kombination für ", recipe_name, " - benötigt weitere Zutaten")
				# Hier könnte später eine Prüfung auf verfügbare Zutaten im Inventar erfolgen
	
	print("[RecipeSystem] Keine Kombination gefunden")
	return false

func get_all_recipes() -> Dictionary:
	return recipes

func get_craftable_items(inventory_items: Array) -> Array:
	var craftable = []
	
	for recipe_name in recipes.keys():
		var recipe = recipes[recipe_name]
		var can_craft = true
		
		# Prüfen ob alle Zutaten vorhanden sind
		for ingredient in recipe["ingredients"]:
			if not ingredient in inventory_items:
				can_craft = false
				break
		
		if can_craft:
			craftable.append(recipe_name)
	
	return craftable

func get_recipe_description(item_name: String) -> String:
	if not has_recipe(item_name):
		return "Unbekanntes Rezept."
	
	var recipe = recipes[item_name]
	var ingredients_str = ""
	for i in range(recipe["ingredients"].size()):
		if i > 0:
			ingredients_str += " + "
		ingredients_str += recipe["ingredients"][i]
	
	return recipe["description"] + " (" + ingredients_str + " → " + str(recipe["result_count"]) + "x " + recipe["result"] + ")"
