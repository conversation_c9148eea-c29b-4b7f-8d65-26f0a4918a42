# ContextStack.gd
# Verwaltet Kontext-Informationen für intelligente Parser-Entscheidungen
# Speichert letzte Aktionen, Items, Orte für Kontext-basierte Rückschlüsse

extends Node
class_name ContextStack

const CommandTypes = preload("res://script/parser/CommandTypes.gd")

var debug_mode := true
var max_history := 10  # Maximale Anzahl gespeicherter Kontext-Einträge

# PHASE 2.7: Item-Genus-Daten für Pronomen-Auflösung
var item_genders: Dictionary = {}

# PHASE 2.7: DEBUG-UI INTEGRATION
var debug_ui: Node = null

# [CursorFix 02.2] Logbook-Integration
var logbook: Node = null

func _ready():
	print("[ContextStack] Initialisiert mit class_name ContextStack")
	if debug_mode:
		print("[ContextStack] Debug-Modus aktiviert")
	load_item_genders()
	
	# [CursorFix 02.2] Versuche Logbook zu finden
	await get_tree().process_frame
	find_logbook()

# [CursorFix 02.2] Suche nach Logbook in verschiedenen Pfaden
func find_logbook() -> void:
	"""Versucht das Logbook in verschiedenen möglichen Pfaden zu finden"""
	var possible_paths = [
		"/root/Main/CoreSystems/Logbook",
		"/root/Main/Systems/Logbook",
		"/root/Main/LogicSystem/Logbook"
	]
	
	for path in possible_paths:
		var potential_logbook = get_node_or_null(path)
		if potential_logbook != null:
			logbook = potential_logbook
			if debug_mode:
				print("[ContextStack] Logbook gefunden unter: ", path)
			return
	
	if debug_mode:
		print("[ContextStack] WARNUNG: Logbook konnte nicht gefunden werden")

# [CursorFix 02.2] Setter für Logbook
func set_logbook(logbook_ref: Node) -> void:
	"""Setzt Referenz zum Logbook manuell"""
	logbook = logbook_ref
	if debug_mode:
		print("[ContextStack] Logbook manuell gesetzt")

# Kontext-Historie
var command_history: Array[String] = []
var item_history: Array[String] = []
var location_history: Array[String] = []
var output_history: Array[String] = []
var action_history: Array[String] = []  # Für push_action()

func push_command(command: String) -> void:
	"""Fügt neuen Command zur Historie hinzu"""
	command_history.push_front(command)
	if command_history.size() > max_history:
		command_history.pop_back()

	if debug_mode:
		print("[ContextStack] Command hinzugefügt: " + command)

func push_item(item: String) -> void:
	"""Fügt neues Item zur Historie hinzu"""
	item_history.push_front(item)
	if item_history.size() > max_history:
		item_history.pop_back()

	if debug_mode:
		print("[ContextStack] Item hinzugefügt: " + item)

func push_location(location: String) -> void:
	"""Fügt neue Location zur Historie hinzu"""
	location_history.push_front(location)
	if location_history.size() > max_history:
		location_history.pop_back()

	if debug_mode:
		print("[ContextStack] Location hinzugefügt: " + location)

func push_action(action: String) -> void:
	"""Fügt neue Aktion zur Historie hinzu"""
	action_history.push_front(action)
	if action_history.size() > max_history:
		action_history.pop_back()

	if debug_mode:
		print("[ContextStack] Aktion hinzugefügt: " + action)

func push_context(type: String, value: String) -> void:
	"""Zentrale Methode: Fügt Kontext basierend auf Typ hinzu"""
	match type.to_lower():
		"item", "target", "object":
			push_item(value)
		"location", "place", "destination":
			push_location(value)
		"action", "command", "verb":
			push_action(value)
		_:
			if debug_mode:
				print("[ContextStack] Unbekannter Kontext-Typ: " + type + " = " + value)

func push_output(output: String) -> void:
	"""Fügt neue Ausgabe zur Historie hinzu und extrahiert Items"""
	output_history.push_front(output)
	if output_history.size() > max_history:
		output_history.pop_back()

	# Items aus Ausgabe extrahieren
	extract_items_from_output(output)

	if debug_mode:
		print("[ContextStack] Output hinzugefügt: " + output.substr(0, 50) + "...")

func get_last(type: String) -> String:
	"""Gibt letzten Eintrag des angegebenen Typs zurück"""
	match type:
		"command":
			return command_history[0] if command_history.size() > 0 else ""
		"item":
			return item_history[0] if item_history.size() > 0 else ""
		"location":
			return location_history[0] if location_history.size() > 0 else ""
		"output":
			return output_history[0] if output_history.size() > 0 else ""
		"action":
			return action_history[0] if action_history.size() > 0 else ""
		_:
			return ""

func has_in_recent(type: String, value: String, count: int = 3) -> bool:
	"""Prüft ob Wert in den letzten N Einträgen vorkommt"""
	var history = []
	match type:
		"command":
			history = command_history
		"item":
			history = item_history
		"location":
			history = location_history
		"output":
			history = output_history
		"action":
			history = action_history
		_:
			return false

	var check_count = min(count, history.size())
	for i in range(check_count):
		if history[i] == value:
			return true
	return false

func infer_from_context(input: String) -> Dictionary:
	"""Versucht aus Kontext zu erraten was gemeint ist"""
	var lowered = input.strip_edges().to_lower()

	if debug_mode:
		print("[ContextStack] infer_from_context aufgerufen mit: '" + lowered + "'")
		print("[ContextStack] Item-Historie: ", item_history)
		print("[ContextStack] Location-Historie: ", location_history)

	var result = {
		"type": CommandTypes.CommandType.UNKNOWN,
		"target": "",
		"confidence": 0.3,
		"origin": "context_guess"
	}

	# PATCH 3.1: SEMANTISCH-BEWUSSTE TAKE-LOGIK
	if lowered in ["nimm", "nehme", "nim", "hebe"]:
		if item_history.size() > 0:
			var best_item = get_best_context_item_for_command(CommandTypes.CommandType.TAKE)
			if best_item["item"] != "":
				result["type"] = CommandTypes.CommandType.TAKE
				result["target"] = best_item["item"]
				result["confidence"] = best_item["confidence"]
				if debug_mode:
					print("[ContextStack] Kontext-Guess: '" + lowered + "' → TAKE " + best_item["item"] + " (confidence: " + str(best_item["confidence"]) + ")")
			else:
				# [CursorFix 02.2] Logbook als Fallback verwenden
				var logbook_item = get_item_from_logbook()
				if logbook_item != "":
					result["type"] = CommandTypes.CommandType.TAKE
					result["target"] = logbook_item
					result["confidence"] = 0.6
					result["origin"] = "logbook_fallback"
					if debug_mode:
						print("[ContextStack] Logbook-Fallback: '" + lowered + "' → TAKE " + logbook_item)
				else:
					if debug_mode:
						print("[ContextStack] Kein nehmbares Item im Kontext oder Logbook gefunden")

	elif lowered in ["gehe", "geh", "gehn"]:
		if location_history.size() > 0:
			result["type"] = CommandTypes.CommandType.MOVE
			result["target"] = location_history[0]
			result["confidence"] = 0.5
			if debug_mode:
				print("[ContextStack] Kontext-Guess: '" + lowered + "' → MOVE " + location_history[0])
		else:
			# [CursorFix 02.2] Logbook als Fallback für Orte
			var logbook_location = get_location_from_logbook()
			if logbook_location != "":
				result["type"] = CommandTypes.CommandType.MOVE
				result["target"] = logbook_location
				result["confidence"] = 0.5
				result["origin"] = "logbook_fallback"
				if debug_mode:
					print("[ContextStack] Logbook-Fallback: '" + lowered + "' → MOVE " + logbook_location)

	elif lowered in ["iss", "essen", "esse", "verzehre"]:
		if item_history.size() > 0:
			var best_item = get_best_context_item_for_command(CommandTypes.CommandType.EAT)
			if best_item["item"] != "":
				result["type"] = CommandTypes.CommandType.EAT
				result["target"] = best_item["item"]
				result["confidence"] = best_item["confidence"]
				if debug_mode:
					print("[ContextStack] Kontext-Guess: '" + lowered + "' → EAT " + best_item["item"] + " (confidence: " + str(best_item["confidence"]) + ")")
			else:
				# [CursorFix 02.2] Logbook als Fallback für essbare Items
				var logbook_food = get_food_from_logbook()
				if logbook_food != "":
					result["type"] = CommandTypes.CommandType.EAT
					result["target"] = logbook_food
					result["confidence"] = 0.5
					result["origin"] = "logbook_fallback"
					if debug_mode:
						print("[ContextStack] Logbook-Fallback: '" + lowered + "' → EAT " + logbook_food)
				else:
					# PATCH 3.1: Rückfrage bei fehlendem essbaren Item
					result["type"] = CommandTypes.CommandType.UNKNOWN
					result["target"] = ""
					result["confidence"] = 0.3
					result["origin"] = "context_no_edible_item"
					if debug_mode:
						print("[ContextStack] Kein essbares Item im Kontext oder Logbook - Rückfrage nötig")
		else:
			# [CursorFix 02.2] Logbook als Fallback für essbare Items
			var logbook_food = get_food_from_logbook()
			if logbook_food != "":
				result["type"] = CommandTypes.CommandType.EAT
				result["target"] = logbook_food
				result["confidence"] = 0.5
				result["origin"] = "logbook_fallback"
				if debug_mode:
					print("[ContextStack] Logbook-Fallback: '" + lowered + "' → EAT " + logbook_food)
			else:
				# Keine Items im Kontext oder Logbook
				result["type"] = CommandTypes.CommandType.UNKNOWN
				result["target"] = ""
				result["confidence"] = 0.2
				result["origin"] = "context_no_items"

	elif lowered in ["trinke", "trink", "trinken"]:
		if item_history.size() > 0:
			var best_item = get_best_context_item_for_command(CommandTypes.CommandType.DRINK)
			if best_item["item"] != "":
				result["type"] = CommandTypes.CommandType.DRINK
				result["target"] = best_item["item"]
				result["confidence"] = best_item["confidence"]
				if debug_mode:
					print("[ContextStack] Kontext-Guess: '" + lowered + "' → DRINK " + best_item["item"] + " (confidence: " + str(best_item["confidence"]) + ")")
			else:
				# [CursorFix 02.2] Logbook als Fallback für trinkbare Items
				var logbook_drink = get_drink_from_logbook()
				if logbook_drink != "":
					result["type"] = CommandTypes.CommandType.DRINK
					result["target"] = logbook_drink
					result["confidence"] = 0.5
					result["origin"] = "logbook_fallback"
					if debug_mode:
						print("[ContextStack] Logbook-Fallback: '" + lowered + "' → DRINK " + logbook_drink)
			else:
				# PATCH 3.1: Rückfrage bei fehlendem trinkbaren Item
				result["type"] = CommandTypes.CommandType.UNKNOWN
				result["target"] = ""
				result["confidence"] = 0.3
				result["origin"] = "context_no_drinkable_item"
				if debug_mode:
						print("[ContextStack] Kein trinkbares Item im Kontext oder Logbook - Rückfrage nötig")
		else:
			# [CursorFix 02.2] Logbook als Fallback für trinkbare Items
			var logbook_drink = get_drink_from_logbook()
			if logbook_drink != "":
				result["type"] = CommandTypes.CommandType.DRINK
				result["target"] = logbook_drink
				result["confidence"] = 0.5
				result["origin"] = "logbook_fallback"
				if debug_mode:
					print("[ContextStack] Logbook-Fallback: '" + lowered + "' → DRINK " + logbook_drink)
			else:
				# Keine Items im Kontext oder Logbook
			result["type"] = CommandTypes.CommandType.UNKNOWN
			result["target"] = ""
			result["confidence"] = 0.2
			result["origin"] = "context_no_items"

	elif lowered in ["schaue", "schau", "umschaun", "umschauen"]:
		result["type"] = CommandTypes.CommandType.LOOK
		result["confidence"] = 0.7
		if debug_mode:
			print("[ContextStack] Kontext-Guess: '" + lowered + "' → LOOK")

	if debug_mode:
		print("[ContextStack] Rückgabe: ", result)

	return result

# [CursorFix 02.2] Logbook-Fallback-Methoden
func get_item_from_logbook() -> String:
	"""Holt ein relevantes Item aus dem Logbook als Fallback"""
	if logbook == null or not logbook.has_method("get_recent_items"):
		if debug_mode:
			print("[ContextStack] Logbook nicht verfügbar oder hat keine get_recent_items Methode")
		return ""
	
	var recent_items = logbook.get_recent_items(3)  # Letzte 3 Items aus Logbook
	if recent_items.size() > 0:
		if debug_mode:
			print("[ContextStack] Item aus Logbook: ", recent_items[0])
		return recent_items[0]
	
	return ""

func get_location_from_logbook() -> String:
	"""Holt einen relevanten Ort aus dem Logbook als Fallback"""
	if logbook == null or not logbook.has_method("get_recent_locations"):
		if debug_mode:
			print("[ContextStack] Logbook nicht verfügbar oder hat keine get_recent_locations Methode")
		return ""
	
	var recent_locations = logbook.get_recent_locations(3)  # Letzte 3 Orte aus Logbook
	if recent_locations.size() > 0:
		if debug_mode:
			print("[ContextStack] Ort aus Logbook: ", recent_locations[0])
		return recent_locations[0]
	
	return ""

func get_food_from_logbook() -> String:
	"""Holt ein essbares Item aus dem Logbook als Fallback"""
	if logbook == null or not logbook.has_method("get_recent_items_by_tag"):
		if debug_mode:
			print("[ContextStack] Logbook nicht verfügbar oder hat keine get_recent_items_by_tag Methode")
		return ""
	
	var food_items = logbook.get_recent_items_by_tag(["nahrung", "essbar", "frucht", "fleisch", "gemüse"], 3)
	if food_items.size() > 0:
		if debug_mode:
			print("[ContextStack] Essbares Item aus Logbook: ", food_items[0])
		return food_items[0]
	
	return ""

func get_drink_from_logbook() -> String:
	"""Holt ein trinkbares Item aus dem Logbook als Fallback"""
	if logbook == null or not logbook.has_method("get_recent_items_by_tag"):
		if debug_mode:
			print("[ContextStack] Logbook nicht verfügbar oder hat keine get_recent_items_by_tag Methode")
		return ""
	
	var drink_items = logbook.get_recent_items_by_tag(["flüssigkeit", "trinkbar", "wasser", "getränk"], 3)
	if drink_items.size() > 0:
		if debug_mode:
			print("[ContextStack] Trinkbares Item aus Logbook: ", drink_items[0])
		return drink_items[0]
	
	return ""

# PROBLEM 2: ROBUSTE TARGET-AUSWERTUNG MIT HÄUFIGKEIT UND ZEITLICHER NÄHE
func get_best_context_item() -> Dictionary:
	"""Ermittelt bestes Item basierend auf Häufigkeit und zeitlicher Nähe"""
	if item_history.size() == 0:
		return {"item": "", "confidence": 0.3}

	var item_scores = {}
	var check_range = min(item_history.size(), 10)  # Betrachte nur letzte 10 Items

	# Berechne Scores für alle Items in der Historie
	for i in range(check_range):
		var item = item_history[i]
		if not item_scores.has(item):
			item_scores[item] = 0.0

		# Zeitliche Nähe: Je aktueller, desto höher (Position 0 = aktuellstes)
		var recency_weight = 1.0 - (float(i) / float(check_range))

		# Häufigkeit: Zähle Vorkommen in der Historie
		var frequency = 0
		for j in range(check_range):
			if item_history[j] == item:
				frequency += 1
		var frequency_weight = min(float(frequency) / 3.0, 1.0)  # Max 3 Vorkommen = 1.0

		# Kombinierter Score: 60% Häufigkeit + 40% zeitliche Nähe
		var combined_score = (frequency_weight * 0.6) + (recency_weight * 0.4)
		item_scores[item] = max(item_scores[item], combined_score)

	# Finde Item mit höchstem Score
	var best_item = ""
	var best_score = 0.0
	for item in item_scores.keys():
		if item_scores[item] > best_score:
			best_score = item_scores[item]
			best_item = item

	# Konvertiere Score zu Confidence (0.5 bis 0.9)
	var confidence = 0.5 + (best_score * 0.4)

	if debug_mode:
		print("[ContextStack] Item-Scores: ", item_scores)
		print("[ContextStack] Bestes Item: '", best_item, "' mit Score: ", best_score, " → Confidence: ", confidence)

	return {"item": best_item, "confidence": confidence}

# PATCH 3.1: SEMANTISCH-BEWUSSTE CONTEXT-AUSWERTUNG
func get_best_context_item_for_command(command_type: CommandTypes.CommandType) -> Dictionary:
	"""Ermittelt bestes Item basierend auf Häufigkeit, zeitlicher Nähe UND semantischer Kompatibilität"""
	if item_history.size() == 0:
		return {"item": "", "confidence": 0.3}

	var item_scores = {}
	var check_range = min(item_history.size(), 10)  # Betrachte nur letzte 10 Items

	# Berechne Scores für alle Items in der Historie
	for i in range(check_range):
		var item = item_history[i]

		# PATCH 3.1: SEMANTISCHE VALIDIERUNG
		if not is_item_compatible_with_command(command_type, item):
			if debug_mode:
				print("[ContextStack] Item '", item, "' nicht kompatibel mit ", command_type)
			continue  # Überspringe inkompatible Items

		if not item_scores.has(item):
			item_scores[item] = 0.0

		# Zeitliche Nähe: Je aktueller, desto höher (Position 0 = aktuellstes)
		var recency_weight = 1.0 - (float(i) / float(check_range))

		# Häufigkeit: Zähle Vorkommen in der Historie
		var frequency = 0
		for j in range(check_range):
			if item_history[j] == item:
				frequency += 1
		var frequency_weight = min(float(frequency) / 3.0, 1.0)  # Max 3 Vorkommen = 1.0

		# Kombinierter Score: 60% Häufigkeit + 40% zeitliche Nähe
		var combined_score = (frequency_weight * 0.6) + (recency_weight * 0.4)
		item_scores[item] = max(item_scores[item], combined_score)

	# Finde Item mit höchstem Score
	var best_item = ""
	var best_score = 0.0
	for item in item_scores.keys():
		if item_scores[item] > best_score:
			best_score = item_scores[item]
			best_item = item

	# Konvertiere Score zu Confidence (0.5 bis 0.9)
	var confidence = 0.5 + (best_score * 0.4) if best_item != "" else 0.3

	if debug_mode:
		print("[ContextStack] Semantisch gefilterte Item-Scores: ", item_scores)
		print("[ContextStack] Bestes kompatibles Item: '", best_item, "' mit Score: ", best_score, " → Confidence: ", confidence)

	return {"item": best_item, "confidence": confidence}

func is_item_compatible_with_command(command_type: CommandTypes.CommandType, item: String) -> bool:
	"""Prüft ob Item semantisch mit Command kompatibel ist"""
	var category = get_semantic_category(item)

	match command_type:
		CommandTypes.CommandType.EAT:
			return category == "edible_item" or category == "unknown"
		CommandTypes.CommandType.DRINK:
			return category == "edible_item" or category == "unknown"  # Kokosnuss ist trinkbar
		CommandTypes.CommandType.USE, CommandTypes.CommandType.CRAFT:
			return category != "location"  # Alles außer Locations
		CommandTypes.CommandType.TAKE:
			return category != "location"  # Alles außer Locations
		_:
			return true  # Andere Commands sind flexibel

func get_semantic_category(word: String) -> String:
	"""PATCH 3.1: Erweiterte semantische Kategorisierung"""
	var lowered = word.to_lower()

	# Locations/Orte
	if lowered in ["strand", "klippe", "wald", "höhle", "lichtung", "quelle", "bucht", "meer", "felsen", "ufer"]:
		return "location"

	# Essbare/Trinkbare Items
	if lowered in ["kokosnuss", "kokosnusshälften", "muschel", "nuss", "beere", "beeren", "frucht", "wasser"]:
		return "edible_item"

	# Werkzeuge/Nicht-essbare Items
	if lowered in ["stein", "feuerstein", "treibholz", "holz", "holzspäne", "messer", "axt", "seil", "stock"]:
		return "non_edible_item"

	# Feuer/Spezielle Items
	if lowered in ["feuer", "lagerfeuer", "großes_feuer", "flamme"]:
		return "fire_item"

	# Default: unbekannt (erlaubt alle Commands)
	return "unknown"

func extract_items_from_output(text: String) -> void:
	"""Extrahiert Items aus Spielausgabe"""
	var known_items = ["kokosnuss", "treibholz", "muschel", "stein", "feuerstein", "seetang",
					   "kokosnusshälften", "holzspäne", "feuer"]

	var lower_text = text.to_lower()

	# "Hier siehst du:" Pattern
	if "hier siehst du:" in lower_text:
		var items_part = lower_text.split("hier siehst du:")[1].split(".")[0]
		for item in known_items:
			if item in items_part:
				push_item(item)

	# Alle erwähnten Items sammeln
	for item in known_items:
		if item in lower_text:
			push_item(item)

func get_last_item() -> String:
	"""REPARATUR: Wrapper für get_last('item') für Pronomen-Erkennung"""
	return get_last("item")

# REPARATUR 1: GET_VISIBLE_ITEMS() FÜR KONTEXTUELLE SUGGESTIONS
func get_visible_items() -> Array:
	"""Gibt alle Items am aktuellen Ort zurück für Suggestions"""
	var visible_items = []

	# Prüfe ob GameStateMaster verfügbar ist
	var game_state = get_node_or_null("/root/Main/CoreSystems/GameStateMaster")
	if game_state == null:
		if debug_mode:
			print("[ContextStack] GameStateMaster nicht gefunden")
		return visible_items

	var current_location = game_state.get_location()
	if current_location.is_empty():
		if debug_mode:
			print("[ContextStack] Kein aktueller Ort verfügbar")
		return visible_items

	# Lade world.json für verfügbare Items
	var world_data = load_world_data()
	if world_data.is_empty() or not world_data.has("locations"):
		if debug_mode:
			print("[ContextStack] World-Daten nicht verfügbar")
		return visible_items

	if world_data["locations"].has(current_location):
		var location_data = world_data["locations"][current_location]
		if location_data.has("items"):
			visible_items = location_data["items"].duplicate()

	if debug_mode:
		print("[ContextStack] Sichtbare Items am Ort '", current_location, "': ", visible_items)

	return visible_items

func load_world_data() -> Dictionary:
	"""Lädt world.json für Item-Verfügbarkeit"""
	var path = "res://data/world.json"
	if not FileAccess.file_exists(path):
		return {}

	var file = FileAccess.open(path, FileAccess.READ)
	if file == null:
		return {}

	var json_text = file.get_as_text()
	file.close()

	var json = JSON.new()
	var parse_result = json.parse(json_text)
	if parse_result != OK:
		return {}

	return json.data

# MODUL 1: NEUE METHODE FÜR TAG-BASIERTE ITEM-FILTERUNG
func get_visible_items_by_tag(tag_list: Array) -> Array[String]:
	"""Gibt alle sichtbaren Items am aktuellen Ort zurück, die mindestens einen der übergebenen Tags enthalten"""
	var filtered_items = []

	if tag_list.is_empty():
		if debug_mode:
			print("[ContextStack] Leere Tag-Liste - keine Filterung möglich")
		return filtered_items

	# Hole alle sichtbaren Items
	var visible_items = get_visible_items()
	if visible_items.is_empty():
		if debug_mode:
			print("[ContextStack] Keine sichtbaren Items verfügbar")
		return filtered_items

	# Lade Item-Daten für Tag-Prüfung
	var items_data = load_items_data()
	if items_data.is_empty():
		if debug_mode:
			print("[ContextStack] Item-Daten nicht verfügbar")
		return filtered_items

	# Filtere Items nach Tags
	for item in visible_items:
		if has_matching_tag(item, tag_list, items_data):
			filtered_items.append(item)

	if debug_mode:
		print("[ContextStack] Gefilterte Items für Tags ", tag_list, ": ", filtered_items)

	return filtered_items

func has_matching_tag(item: String, tag_list: Array, items_data: Dictionary) -> bool:
	"""Prüft ob Item mindestens einen der gewünschten Tags hat"""
	if not items_data.has(item):
		return false

	var item_data = items_data[item]
	if not item_data.has("tags"):
		return false

	var item_tags = item_data["tags"]
	if not item_tags is Array:
		return false

	# Prüfe ob mindestens ein Tag übereinstimmt
	for tag in tag_list:
		if tag in item_tags:
			return true

	return false

func load_items_data() -> Dictionary:
	"""Lädt items.json für Tag-Informationen"""
	var path = "res://data/items.json"
	if not FileAccess.file_exists(path):
		if debug_mode:
			print("[ContextStack] items.json nicht gefunden: ", path)
		return {}

	var file = FileAccess.open(path, FileAccess.READ)
	if file == null:
		if debug_mode:
			print("[ContextStack] Kann items.json nicht öffnen")
		return {}

	var json_text = file.get_as_text()
	file.close()

	var json = JSON.new()
	var parse_result = json.parse(json_text)
	if parse_result != OK:
		if debug_mode:
			print("[ContextStack] JSON Parse-Fehler in items.json")
		return {}

	return json.data

# PHASE 2.7: GENUS-BASIERTE PRONOMEN-AUFLÖSUNG
func load_item_genders() -> void:
	"""Lädt Item-Genus-Daten aus items.json"""
	var file = FileAccess.open("res://script/data/items.json", FileAccess.READ)
	if file == null:
		if debug_mode:
			print("[ContextStack] WARNUNG: items.json nicht gefunden")
		return

	var json_text = file.get_as_text()
	file.close()

	var json = JSON.new()
	var parse_result = json.parse(json_text)
	if parse_result != OK:
		if debug_mode:
			print("[ContextStack] FEHLER: items.json Parse-Fehler")
		return

	var items_data = json.data
	for item_name in items_data.keys():
		var item_data = items_data[item_name]
		if item_data.has("gender"):
			item_genders[item_name] = item_data["gender"]

	if debug_mode:
		print("[ContextStack] Item-Genus geladen: ", item_genders)

func get_item_by_pronoun(pronoun: String) -> String:
	"""PHASE 2.7: Findet passendes Item basierend auf Pronomen-Genus"""
	var target_gender = ""

	# Pronomen zu Genus zuordnen
	match pronoun.to_lower():
		"sie", "ihr", "ihre":
			target_gender = "f"  # feminin
		"es", "das":
			target_gender = "n"  # neutral
		"er", "ihn", "ihm", "seiner":
			target_gender = "m"  # maskulin
		_:
			if debug_mode:
				print("[ContextStack] Unbekanntes Pronomen: ", pronoun)
			return ""

	# Suche letztes Item mit passendem Genus
	for item in item_history:
		# PHASE 2.7: SICHERHEITSPRÜFUNG - Nur wenn Gender-Daten vorhanden
		if item_genders.has(item) and item_genders[item] == target_gender:
			if debug_mode:
				print("[ContextStack] Genus-Match: '", pronoun, "' (", target_gender, ") → ", item)
			# PHASE 2.7: Debug-UI Integration - KÜRZER
			if debug_ui != null and debug_ui.has_method("update_pronoun_resolution"):
				debug_ui.update_pronoun_resolution(pronoun, item, "genus")
			return item

	# Fallback: letztes Item unabhängig vom Genus
	if item_history.size() > 0:
		if debug_mode:
			print("[ContextStack] Genus-Fallback: '", pronoun, "' → ", item_history[0])
		# PHASE 2.7: Debug-UI Integration
		if debug_ui != null and debug_ui.has_method("update_pronoun_resolution"):
			debug_ui.update_pronoun_resolution(pronoun, item_history[0], "fallback")
		return item_history[0]

	if debug_mode:
		print("[ContextStack] Kein Item für Pronomen '", pronoun, "' gefunden")
	return ""

func clear_history() -> void:
	"""Löscht gesamte Kontext-Historie"""
	command_history.clear()
	item_history.clear()
	location_history.clear()
	output_history.clear()
	action_history.clear()

	if debug_mode:
		print("[ContextStack] Historie gelöscht")

# SPRINT 03: [03.1] - CommandType-spezifische Kontextinferenz
func infer_for_command_type(command_type: CommandTypes.CommandType) -> Dictionary:
	"""Ermittelt passendes Target aus dem Kontext für einen bekannten CommandType"""
	if debug_mode:
		print("[ContextStack] infer_for_command_type aufgerufen für CommandType: ", command_type)

	var result = {
		"type": command_type,
		"target": "",
		"confidence": 0.0,
		"origin": "context_command_specific"
	}
	
	# CommandType-spezifische Empfehlungen
	match command_type:
		CommandTypes.CommandType.TAKE:
			# Für TAKE: Suche nach nehmbaren Items
			var best_item = get_best_context_item_for_command(CommandTypes.CommandType.TAKE)
			if best_item["item"] != "":
				result["target"] = best_item["item"]
				result["confidence"] = best_item["confidence"]
				if debug_mode:
					print("[ContextStack] Take-Target gefunden: ", best_item["item"])
			else:
				# Fallback auf sichtbare Items am aktuellen Ort
				var visible_items = get_visible_items()
				if visible_items.size() > 0:
					result["target"] = visible_items[0]
					result["confidence"] = 0.7  # Mittlere Confidence für sichtbare Items
					if debug_mode:
						print("[ContextStack] Take-Target aus sichtbaren Items: ", visible_items[0])
				
				# Logbook als Fallback
				if result["target"] == "" and logbook != null and logbook.has_method("get_recent_items"):
					var recent_items = logbook.get_recent_items(3)
					if recent_items.size() > 0:
						result["target"] = recent_items[0]
						result["confidence"] = 0.6  # Niedrigere Confidence für Logbook-Fallback
						if debug_mode:
							print("[ContextStack] Take-Target aus Logbook: ", recent_items[0])
		
		CommandTypes.CommandType.MOVE:
			# Für MOVE: Suche nach Orten
			if location_history.size() > 0:
				result["target"] = location_history[0]
				result["confidence"] = 0.8
				if debug_mode:
					print("[ContextStack] Move-Target gefunden: ", location_history[0])
			
			# Logbook als Fallback
			if result["target"] == "" and logbook != null and logbook.has_method("get_recent_locations"):
				var recent_locations = logbook.get_recent_locations(3)
				if recent_locations.size() > 0:
					result["target"] = recent_locations[0]
					result["confidence"] = 0.7
					if debug_mode:
						print("[ContextStack] Move-Target aus Logbook: ", recent_locations[0])
		
		CommandTypes.CommandType.EAT:
			# Für EAT: Suche nach essbaren Items
			var best_item = get_best_context_item_for_command(CommandTypes.CommandType.EAT)
			if best_item["item"] != "":
				result["target"] = best_item["item"]
				result["confidence"] = best_item["confidence"]
				if debug_mode:
					print("[ContextStack] Eat-Target gefunden: ", best_item["item"])
			else:
				# Fallback auf essbare Items aus Logbook oder Inventarsystem
				if logbook != null and logbook.has_method("get_recent_items_by_tag"):
					var food_items = logbook.get_recent_items_by_tag(["nahrung", "essbar", "frucht"], 3)
					if food_items.size() > 0:
						result["target"] = food_items[0]
						result["confidence"] = 0.65
						if debug_mode:
							print("[ContextStack] Eat-Target aus Logbook: ", food_items[0])
		
		CommandTypes.CommandType.DRINK:
			# Für DRINK: Suche nach trinkbaren Items
			var best_item = get_best_context_item_for_command(CommandTypes.CommandType.DRINK)
			if best_item["item"] != "":
				result["target"] = best_item["item"]
				result["confidence"] = best_item["confidence"]
				if debug_mode:
					print("[ContextStack] Drink-Target gefunden: ", best_item["item"])
			else:
				# Fallback auf trinkbare Items aus Logbook
				if logbook != null and logbook.has_method("get_recent_items_by_tag"):
					var drink_items = logbook.get_recent_items_by_tag(["flüssigkeit", "trinkbar"], 3)
					if drink_items.size() > 0:
						result["target"] = drink_items[0]
						result["confidence"] = 0.65
						if debug_mode:
							print("[ContextStack] Drink-Target aus Logbook: ", drink_items[0])
		
		CommandTypes.CommandType.USE:
			# Für USE: Suche nach benutzbaren Items
			if item_history.size() > 0:
				result["target"] = item_history[0]
				result["confidence"] = 0.7
				if debug_mode:
					print("[ContextStack] Use-Target gefunden: ", item_history[0])
		
		CommandTypes.CommandType.CRAFT:
			# Für CRAFT: Suche nach Rezeptkomponenten oder fertigen Rezepten
			if item_history.size() > 0:
				result["target"] = item_history[0]
				result["confidence"] = 0.6  # Niedrigere Confidence, da CRAFT meist mehrere Items braucht
				if debug_mode:
					print("[ContextStack] Craft-Target-Komponente gefunden: ", item_history[0])
		
		CommandTypes.CommandType.LOOK:
			# Für LOOK: Fallback auf Umgebung oder aktuelle Location
			result["target"] = ""  # LOOK benötigt kein spezifisches Target
			result["confidence"] = 0.9
			if debug_mode:
				print("[ContextStack] Look benötigt kein Target")
	
	if debug_mode:
		print("[ContextStack] infer_for_command_type Ergebnis: ", result)
	
	return result