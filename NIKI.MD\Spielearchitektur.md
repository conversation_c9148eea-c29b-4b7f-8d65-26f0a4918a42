# Spielearchitektur

## 1. Ziel des Spiels

Unser Spiel ist ein textbasiertes Survival-Adventure, in dem der Spieler mit einer KI-gesteuerten Figur namens Niki interagiert. Ziel ist es, auf einer Insel zu überleben, Ressourcen zu finden und sich letztlich einen Fluchtweg zu erarbeiten. Die Kommunikation mit dem Spiel erfolgt in natürlicher Sprache. Die Engine verarbeitet Spielereingaben über einen Parser und wandelt sie in Spielaktionen um.

Aktuell befinden wir uns im Aufbau eines Vertical Slice (Prototyp):

* Text rein → Text raus
* Später: Voice rein → Voice raus

## 2. Zentrale Steuerung (Autoloads)

Folgende Autoloads sind für den Kern des Spiels verantwortlich:

* **GameMaster.gd**: Zentrale Steuerungsinstanz für Spielverlauf, Initialisierung und globale Flags. Enthält den zentralen Einstiegspunkt und verteilt Kommandos an Parser und Handler.
* **GameStateMaster.gd**: Verwaltet den aktuellen Spielstatus (z. B. Tageszeit, Standort, aktive Effekte).
* **StatusMaster.gd**: Hält die Statuswerte von Niki (Hunger, Durst, Energie, Composure).
* **TimeMaster.gd**: Verarbeitet alle zeitlichen Aspekte. Jede Aktion kostet Zeit.
* **WeatherMaster.gd**: Simuliert das Wetter (aktuell: Dummy, später dynamisch mit Events wie Regen, Erdrutsch).
* **InputSystem.gd**: Leitet die Nutzereingaben an LUCI weiter.
* **Logbook.gd**: Hält strukturierte Spielereignisse (z. B. "Muschel genommen", "Feuer gemacht").
* **MemoryMaster.gd**: Noch rudimentär. Später geplant für langfristige Wissensspeicherung.
* **SaveSystem.gd**: Save- und Autospeicherfunktion (noch rudimentär).

**Hinweis**: Der Debug-Modus ist permanent aktiv. `GameMaster.debug_mode = true`

## 3. Parser-System: LUCI

LUCI ist unser modular aufgebautes Parsing-System. Es verarbeitet Spielereingaben (in natürlicher Sprache) und wandelt sie in maschinenlesbare Befehle um. Diese werden als `ParsedCommand` an den GameMaster übergeben.

### Ablauf der Parsing-Kette:

1. **SyntaxTagger**: Zerlegt den Satz in Bestandteile (Verben, Objekte etc.)
2. **AliasResolver**: Erkennt Synonyme (z. B. "anschauen" → "look")
3. **CommandTypeMatcher**: Ordnet den Befehl einem `CommandType` zu (z. B. TAKE, EAT, USE)
4. **ContextStack**: Versucht, fehlende Informationen aus dem Kontext zu ergänzen (z. B. Zielobjekt aus vorheriger Eingabe)
5. **FuzzyMemory**: Erkennt mögliche Tippfehler (z. B. "nim" → "nimm")
6. **CollisionResolver**: Erkennt widersprüchliche Ziele (z. B. "iss Stein") und bietet Alternativen an
7. **UnclearResolver**: Falls keine eindeutige Interpretation möglich ist, werden Vorschläge gemacht (max. 3 bei niedriger Confidence)
8. **TaskQueue + ParsedCommand**: Am Ende entsteht ein klar strukturierter `ParsedCommand`, z. B.:

```json
{
  "command_type": "TAKE",
  "target": "muschel",
  "origin": "direct"
}
```

## 4. Handler

Jeder CommandType hat einen zugehörigen Handler (in `/handlers/`):

* **LookHandler**: z. B. `schau muschel an`
* **TakeHandler**: z. B. `nimm muschel`
* **UseHandler**: z. B. `benutze stein mit kokosnuss` (kann Craft triggern)
* **CraftHandler**: z. B. `baue feuer`, `kombiniere stein + kokosnuss`
* **EatHandler**: z. B. `iss beeren`
* **DrinkHandler**: z. B. `trink wasser`
* **CookHandler**: z. B. `koche suppe`
* **FireHandler**: z. B. `zünde feuer an`
* **MoveHandler**: z. B. `geh zum strand`
* **SearchHandler**: z. B. `durchsuche wrack`

**Rückgaben der Handler müssen maschinenlesbar sein.**

## 5. Systeme (in `/systems/`)

* **InventorySystem.gd**: Verwaltet Items, Slots und Gewicht
* **RecipeSystem.gd**: Definiert Crafting- und Kochrezepte
* **NutritionSystem.gd**: Berechnet Nahrungswert / Bonus / Schaden
* **EventSystem.gd**: Geplante Events, z. B. Regen, Erdrutsch, Zufallsfunde
* **ShelterSystem.gd**: Noch rudimentär, aber vorgesehen für Wetterschutz und Schlaf
* **YellowAngel.gd**: Steuert Hilfe-Systeme:

  * `hilfe [ziel]`: Basisanleitung
  * `tipp [ziel]`: strategischer Hinweis
  * `info [ziel]`: Beschreibung (gleich wie LookHandler)

## 6. Datenstrukturen

Alle Daten werden in `/data/` als `.json` gepflegt und beim Start geladen:

* **items.json**: Enthält alle Objekte
* **recipes.json**: Rezepte für Crafting/Kochen
* **world.json**: Orte, Wege, Geografie
* **flags.json**: Spielinterne Trigger
* **status\_effects.json**: Auswirkungen wie Koffein, Rausch, Verletzung
* **settings.json**: Zentrale Konfiguration
* **saves\_autoslot.json**: Speicherstand

**DataLoader geplant, aber noch nicht implementiert.** Aktuell laden Module ihre Daten selbst.

## 7. Debug-Modus

`GameMaster.debug_mode` ist aktiv. Cursor darf Debug-Features jederzeit nutzen. Keine Einschränkung.

## 8. Zukunft (nicht implementiert)

Diese Konzepte sind **nicht Teil des aktuellen Spiels**, aber relevant für spätere Erweiterungen:

* **Voice-Output** → gesprochene Niki-Ausgaben
* **Emotion-Modul** für dynamische Niki-Stimmungen
* **Minimap mit Raster** (aktuell geplant)
* **Skill-System / Levelaufstieg**
* **Erkennung von Pflanzen / Gefahr durch Spielerfehlverhalten**

Aktueller Fokus: Parser + Basis-Spielsysteme

## 9. Verhaltensregeln für Coding-Agents

* Immer zuerst `NIKI.MD` einlesen
* Keine neuen Systeme oder Begriffe einbauen, die nicht spezifiziert wurden
* Wenn unklar: **Fragen stellen, nicht raten**
* Nur architekturkonforme Lösungen mit sauberer Trennung in Handler, Systeme, Daten
* Keine Spontanlösungen ohne Begründung
* Reviewpflicht aktiv: Jeder Vorschlag muss begründet und überprüft sein

---

Letztes Update: 30.06.2025, 22:00 Uhr
