extends Node

class_name InventorySystem

# Struktur: { "item_name": { "count": 1, "condition": "neu" } }
var items: Dictionary = {}
var data_loader = null  # Referenz zum DataLoader

func _ready():
	# <PERSON><PERSON> einen Frame, um sicherzustellen, dass DataLoader bereit ist
	await get_tree().process_frame
	# Versuche DataLoader zu finden
	data_loader = get_node_or_null("/root/Main/CoreSystems/DataLoader")
	if data_loader != null:
		print("[InventorySystem] DataLoader gefunden")

func add_item(item_name: String, count := 1) -> void:
	if items.has(item_name):
		items[item_name]["count"] += count
	else:
		items[item_name] = {
			"count": count,
			"condition": "neu"  # später erweiterbar
		}

func remove_item(item_name: String, count := 1) -> void:
	if not items.has(item_name):
		return

	items[item_name]["count"] -= count
	if items[item_name]["count"] <= 0:
		items.erase(item_name)

func has_item(item_name: String) -> bool:
	return items.has(item_name)

func list_items() -> Array[String]:
	var result: Array[String] = []
	for item_name in items.keys():
		var count = items[item_name]["count"]
		result.append(item_name + (" x" + str(count) if count > 1 else ""))

	return result

func clear_inventory() -> void:
	items.clear()

# MODUL 3: SEMANTISCHE KOMPATIBILITÄTSPRÜFUNG
func is_usable_for(item_id: String, command_type) -> bool:
	"""Überprüft, ob ein Item semantisch für einen bestimmten Befehl geeignet ist"""
	# SPRINT 02: Verwende DataLoader statt direktem Dateizugriff
	var items_data = load_items_data()
	if items_data.is_empty():
		# Fallback: Wenn keine Daten verfügbar, erlaube alles
		return true

	if not items_data.has(item_id):
		# Item nicht in Datenbank - erlaube als Fallback
		return true

	var item_data = items_data[item_id]
	if not item_data.has("tags"):
		# Keine Tags definiert - erlaube als Fallback
		return true

	var item_tags = item_data["tags"]
	if not item_tags is Array:
		# Ungültige Tag-Struktur - erlaube als Fallback
		return true

	# Prüfe semantische Kompatibilität basierend auf CommandType
	match command_type:
		1: # EAT - CommandTypes.CommandType.EAT
			return has_any_tag(item_tags, ["nahrung", "essbar", "frucht", "fleisch", "gemüse"])
		2: # DRINK - CommandTypes.CommandType.DRINK
			return has_any_tag(item_tags, ["flüssigkeit", "trinkbar", "wasser", "getränk"])
		6: # CRAFT - CommandTypes.CommandType.CRAFT
			return has_any_tag(item_tags, ["material", "bauholz", "zunder", "werkzeug", "rohstoff", "handwerk"])
		7: # USE - CommandTypes.CommandType.USE
			return has_any_tag(item_tags, ["werkzeug", "verwendbar", "nutzbar", "instrument"])
		_:
			# Für andere Commands (TAKE, MOVE, etc.) - immer erlaubt
			return true

func has_any_tag(item_tags: Array, required_tags: Array) -> bool:
	"""Hilfsfunktion: Prüft ob mindestens ein Tag übereinstimmt"""
	for tag in required_tags:
		if tag in item_tags:
			return true
	return false

func load_items_data() -> Dictionary:
	"""Lädt items.json für Tag-Informationen"""
	# SPRINT 02: Verwende DataLoader statt direktem Dateizugriff
	if data_loader != null:
		return data_loader.get_items_data()
	
	# Fallback: Direkter Dateizugriff
	var path = "res://script/data/items.json"  # KORREKTUR: Pfad angepasst
	if not FileAccess.file_exists(path):
		print("[InventorySystem] FEHLER: items.json nicht gefunden bei: " + path)
		return {}

	var file = FileAccess.open(path, FileAccess.READ)
	if file == null:
		var error = FileAccess.get_open_error()
		print("[InventorySystem] FEHLER: Kann items.json nicht öffnen. Error: ", error)
		return {}

	var json_text = file.get_as_text()
	file.close()

	var json = JSON.new()
	var parse_result = json.parse(json_text)
	if parse_result != OK:
		print("[InventorySystem] FEHLER: JSON-Parse-Fehler in items.json")
		return {}

	return json.data
